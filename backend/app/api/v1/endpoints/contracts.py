"""
合同相关API端点
"""
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, BackgroundTasks
from fastapi.responses import J<PERSON>NResponse
from sqlalchemy.orm import Session
from typing import List, Optional
from pathlib import Path
import shutil
import uuid
from datetime import datetime

from app.core.database import get_db
from app.core.config import settings
from app.models.contract import Contract, ContractStatus, RiskAssessment, ClauseAnalysis, ComplianceCheck
from app.services.document_processor import DocumentProcessor
from app.services.ai_analyzer import AIAnalyzer
from app.schemas.contract import (
    ContractCreate, ContractResponse, ContractListResponse,
    AnalysisResponse, RiskAssessmentResponse
)
from loguru import logger

router = APIRouter()

# 初始化服务
document_processor = DocumentProcessor()
ai_analyzer = AIAnalyzer()


@router.post("/upload", response_model=ContractResponse)
async def upload_contract(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    title: Optional[str] = None,
    contract_type: Optional[str] = "通用合同",
    db: Session = Depends(get_db)
):
    """
    上传合同文件
    """
    # 验证文件类型
    file_extension = Path(file.filename).suffix.lower()
    if file_extension not in settings.ALLOWED_FILE_TYPES:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的文件类型: {file_extension}"
        )
    
    # 验证文件大小
    if file.size > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=400,
            detail=f"文件大小超过限制: {settings.MAX_FILE_SIZE / 1024 / 1024}MB"
        )
    
    # 生成唯一文件名
    file_id = str(uuid.uuid4())
    file_name = f"{file_id}{file_extension}"
    file_path = settings.UPLOAD_DIR / file_name
    
    try:
        # 保存文件
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 创建合同记录
        contract = Contract(
            title=title or file.filename,
            file_name=file.filename,
            file_path=str(file_path),
            file_size=file.size,
            file_type=file_extension,
            contract_type=contract_type,
            status=ContractStatus.UPLOADED
        )
        
        db.add(contract)
        db.commit()
        db.refresh(contract)
        
        # 后台处理文档
        background_tasks.add_task(process_contract_background, contract.id, file_path)
        
        logger.info(f"合同上传成功: {contract.id}")
        
        return ContractResponse.from_orm(contract)
        
    except Exception as e:
        # 清理文件
        if file_path.exists():
            file_path.unlink()
        
        logger.error(f"合同上传失败: {str(e)}")
        raise HTTPException(status_code=500, detail="文件上传失败")


@router.get("/", response_model=List[ContractListResponse])
async def list_contracts(
    skip: int = 0,
    limit: int = 20,
    status: Optional[ContractStatus] = None,
    contract_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取合同列表
    """
    query = db.query(Contract)
    
    if status:
        query = query.filter(Contract.status == status)
    
    if contract_type:
        query = query.filter(Contract.contract_type == contract_type)
    
    contracts = query.offset(skip).limit(limit).all()
    
    return [ContractListResponse.from_orm(contract) for contract in contracts]


@router.get("/{contract_id}", response_model=ContractResponse)
async def get_contract(contract_id: int, db: Session = Depends(get_db)):
    """
    获取合同详情
    """
    contract = db.query(Contract).filter(Contract.id == contract_id).first()
    
    if not contract:
        raise HTTPException(status_code=404, detail="合同不存在")
    
    return ContractResponse.from_orm(contract)


@router.get("/{contract_id}/analysis", response_model=AnalysisResponse)
async def get_contract_analysis(contract_id: int, db: Session = Depends(get_db)):
    """
    获取合同分析结果
    """
    contract = db.query(Contract).filter(Contract.id == contract_id).first()
    
    if not contract:
        raise HTTPException(status_code=404, detail="合同不存在")
    
    if contract.status not in [ContractStatus.ANALYZED, ContractStatus.REVIEWED]:
        raise HTTPException(status_code=400, detail="合同尚未分析完成")
    
    # 获取相关分析数据
    risk_assessments = db.query(RiskAssessment).filter(
        RiskAssessment.contract_id == contract_id
    ).all()
    
    clause_analyses = db.query(ClauseAnalysis).filter(
        ClauseAnalysis.contract_id == contract_id
    ).all()
    
    compliance_checks = db.query(ComplianceCheck).filter(
        ComplianceCheck.contract_id == contract_id
    ).all()
    
    return AnalysisResponse(
        contract=ContractResponse.from_orm(contract),
        risk_assessments=[RiskAssessmentResponse.from_orm(ra) for ra in risk_assessments],
        clause_analyses=clause_analyses,
        compliance_checks=compliance_checks
    )


@router.post("/{contract_id}/reanalyze")
async def reanalyze_contract(
    contract_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    重新分析合同
    """
    contract = db.query(Contract).filter(Contract.id == contract_id).first()
    
    if not contract:
        raise HTTPException(status_code=404, detail="合同不存在")
    
    # 更新状态
    contract.status = ContractStatus.PROCESSING
    db.commit()
    
    # 后台重新处理
    background_tasks.add_task(process_contract_background, contract_id, Path(contract.file_path))
    
    return {"message": "重新分析已开始"}


@router.delete("/{contract_id}")
async def delete_contract(contract_id: int, db: Session = Depends(get_db)):
    """
    删除合同
    """
    contract = db.query(Contract).filter(Contract.id == contract_id).first()
    
    if not contract:
        raise HTTPException(status_code=404, detail="合同不存在")
    
    # 删除文件
    file_path = Path(contract.file_path)
    if file_path.exists():
        file_path.unlink()
    
    # 删除数据库记录
    db.delete(contract)
    db.commit()
    
    return {"message": "合同删除成功"}


async def process_contract_background(contract_id: int, file_path: Path):
    """
    后台处理合同的任务
    """
    from app.core.database import SessionLocal
    
    db = SessionLocal()
    
    try:
        contract = db.query(Contract).filter(Contract.id == contract_id).first()
        if not contract:
            logger.error(f"合同不存在: {contract_id}")
            return
        
        # 更新状态为处理中
        contract.status = ContractStatus.PROCESSING
        db.commit()
        
        logger.info(f"开始处理合同: {contract_id}")
        
        # 1. 文档处理
        extracted_content = await document_processor.process_document(file_path)
        
        # 更新合同信息
        contract.extracted_text = extracted_content.text
        
        # 提取合同结构信息
        structure = document_processor.extract_contract_structure(extracted_content.text)
        
        # 从结构中提取基本信息
        if structure.get('parties'):
            contract.parties = structure['parties']
        
        # 2. AI分析
        analysis_result = await ai_analyzer.analyze_contract(
            extracted_content.text, 
            contract.contract_type
        )
        
        # 更新分析结果
        contract.analysis_summary = analysis_result.summary
        contract.key_terms = analysis_result.key_terms
        contract.recommendations = analysis_result.recommendations
        contract.overall_risk_level = analysis_result.risk_assessment.get('overall_level', 'medium')
        contract.overall_risk_score = analysis_result.risk_assessment.get('overall_score', 0.5)
        contract.status = ContractStatus.ANALYZED
        contract.analyzed_at = datetime.utcnow()
        
        # 3. 保存风险评估
        for category, risk_info in analysis_result.risk_assessment.items():
            if category in ['overall_level', 'overall_score']:
                continue
                
            if isinstance(risk_info, dict):
                risk_assessment = RiskAssessment(
                    contract_id=contract_id,
                    risk_category=category,
                    risk_level=risk_info.get('level', 'medium'),
                    risk_score=risk_info.get('score', 0.5),
                    description=risk_info.get('description', ''),
                    impact=risk_info.get('impact', ''),
                    mitigation=risk_info.get('mitigation', '')
                )
                db.add(risk_assessment)
        
        # 4. 保存条款分析
        for clause_data in analysis_result.clause_analysis:
            clause_analysis = ClauseAnalysis(
                contract_id=contract_id,
                clause_type=clause_data.get('clause_type', ''),
                clause_content=clause_data.get('clause_content', ''),
                clause_position=clause_data.get('position', ''),
                is_standard=clause_data.get('is_standard', False),
                is_favorable=clause_data.get('is_favorable'),
                completeness_score=clause_data.get('completeness_score', 0.5),
                clarity_score=clause_data.get('clarity_score', 0.5),
                issues=clause_data.get('issues', []),
                suggestions=clause_data.get('suggestions', [])
            )
            db.add(clause_analysis)
        
        # 5. 保存合规检查
        compliance_data = analysis_result.compliance_check
        if compliance_data.get('checks'):
            for check in compliance_data['checks']:
                compliance_check = ComplianceCheck(
                    contract_id=contract_id,
                    regulation_type=check.get('regulation', ''),
                    check_result=check.get('status', 'warning'),
                    compliance_score=compliance_data.get('compliance_score', 0.7),
                    requirements=[check.get('description', '')],
                    recommendations=[check.get('recommendation', '')]
                )
                db.add(compliance_check)
        
        db.commit()
        logger.info(f"合同处理完成: {contract_id}")
        
    except Exception as e:
        logger.error(f"合同处理失败: {contract_id}, 错误: {str(e)}")
        
        # 更新状态为失败
        contract = db.query(Contract).filter(Contract.id == contract_id).first()
        if contract:
            contract.status = ContractStatus.UPLOADED  # 回退状态
            db.commit()
    
    finally:
        db.close()
