"""
合同相关数据模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Float, JSON, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum
from typing import Optional, List, Dict, Any

from app.core.database import Base


class ContractStatus(str, Enum):
    """合同状态枚举"""
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    ANALYZED = "analyzed"
    REVIEWED = "reviewed"
    APPROVED = "approved"
    REJECTED = "rejected"


class RiskLevel(str, Enum):
    """风险等级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class Contract(Base):
    """合同主表"""
    __tablename__ = "contracts"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False, comment="合同标题")
    file_name = Column(String(255), nullable=False, comment="文件名")
    file_path = Column(String(500), nullable=False, comment="文件路径")
    file_size = Column(Integer, comment="文件大小(字节)")
    file_type = Column(String(50), comment="文件类型")
    
    # 合同基本信息
    contract_type = Column(String(100), comment="合同类型")
    parties = Column(JSON, comment="合同当事方")
    contract_date = Column(DateTime, comment="合同签署日期")
    effective_date = Column(DateTime, comment="生效日期")
    expiry_date = Column(DateTime, comment="到期日期")
    contract_value = Column(Float, comment="合同金额")
    
    # 状态和处理信息
    status = Column(String(20), default=ContractStatus.UPLOADED, comment="处理状态")
    overall_risk_level = Column(String(20), comment="整体风险等级")
    overall_risk_score = Column(Float, comment="整体风险评分")
    
    # 分析结果
    extracted_text = Column(Text, comment="提取的文本内容")
    key_terms = Column(JSON, comment="关键条款")
    analysis_summary = Column(Text, comment="分析摘要")
    recommendations = Column(JSON, comment="建议")
    
    # 时间戳
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")
    analyzed_at = Column(DateTime, comment="分析完成时间")
    
    # 用户信息
    uploaded_by = Column(Integer, ForeignKey("users.id"), comment="上传用户ID")
    
    # 关联关系
    risk_assessments = relationship("RiskAssessment", back_populates="contract")
    clause_analyses = relationship("ClauseAnalysis", back_populates="contract")
    compliance_checks = relationship("ComplianceCheck", back_populates="contract")


class RiskAssessment(Base):
    """风险评估表"""
    __tablename__ = "risk_assessments"
    
    id = Column(Integer, primary_key=True, index=True)
    contract_id = Column(Integer, ForeignKey("contracts.id"), nullable=False)
    
    # 风险分类
    risk_category = Column(String(100), nullable=False, comment="风险类别")
    risk_type = Column(String(100), comment="风险类型")
    risk_level = Column(String(20), nullable=False, comment="风险等级")
    risk_score = Column(Float, nullable=False, comment="风险评分")
    
    # 风险详情
    description = Column(Text, comment="风险描述")
    impact = Column(Text, comment="影响分析")
    likelihood = Column(Float, comment="发生概率")
    mitigation = Column(Text, comment="缓解措施")
    
    # 相关条款
    related_clauses = Column(JSON, comment="相关条款")
    
    created_at = Column(DateTime, server_default=func.now())
    
    # 关联关系
    contract = relationship("Contract", back_populates="risk_assessments")


class ClauseAnalysis(Base):
    """条款分析表"""
    __tablename__ = "clause_analyses"
    
    id = Column(Integer, primary_key=True, index=True)
    contract_id = Column(Integer, ForeignKey("contracts.id"), nullable=False)
    
    # 条款信息
    clause_type = Column(String(100), nullable=False, comment="条款类型")
    clause_title = Column(String(255), comment="条款标题")
    clause_content = Column(Text, nullable=False, comment="条款内容")
    clause_position = Column(String(50), comment="条款位置")
    
    # 分析结果
    is_standard = Column(Boolean, default=False, comment="是否为标准条款")
    is_favorable = Column(Boolean, comment="是否有利")
    completeness_score = Column(Float, comment="完整性评分")
    clarity_score = Column(Float, comment="清晰度评分")
    
    # 问题和建议
    issues = Column(JSON, comment="发现的问题")
    suggestions = Column(JSON, comment="修改建议")
    
    created_at = Column(DateTime, server_default=func.now())
    
    # 关联关系
    contract = relationship("Contract", back_populates="clause_analyses")


class ComplianceCheck(Base):
    """合规性检查表"""
    __tablename__ = "compliance_checks"
    
    id = Column(Integer, primary_key=True, index=True)
    contract_id = Column(Integer, ForeignKey("contracts.id"), nullable=False)
    
    # 合规检查信息
    regulation_type = Column(String(100), nullable=False, comment="法规类型")
    regulation_name = Column(String(255), comment="法规名称")
    check_result = Column(String(20), nullable=False, comment="检查结果")
    compliance_score = Column(Float, comment="合规评分")
    
    # 详细信息
    requirements = Column(JSON, comment="合规要求")
    violations = Column(JSON, comment="违规项")
    recommendations = Column(JSON, comment="合规建议")
    
    created_at = Column(DateTime, server_default=func.now())
    
    # 关联关系
    contract = relationship("Contract", back_populates="compliance_checks")
