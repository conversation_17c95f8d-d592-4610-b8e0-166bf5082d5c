"""
RAG系统配置模块
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import os


class RetrievalStrategy(Enum):
    """检索策略枚举"""
    VECTOR_ONLY = "vector_only"
    KEYWORD_ONLY = "keyword_only"
    HYBRID = "hybrid"
    SEMANTIC = "semantic"


class EmbeddingModel(Enum):
    """嵌入模型枚举"""
    QWEN_V3 = "text-embedding-v3"
    OPENAI_LARGE = "text-embedding-3-large"
    BGE_LARGE_ZH = "BAAI/bge-large-zh-v1.5"
    M3E_LARGE = "moka-ai/m3e-large"


@dataclass
class VectorStoreConfig:
    """向量存储配置"""
    provider: str = "chroma"  # chroma, weaviate, pinecone, milvus
    host: str = "localhost"
    port: int = 8000
    collection_name: str = "contract_knowledge"
    distance_metric: str = "cosine"
    index_type: str = "hnsw"
    
    # ChromaDB特定配置
    persist_directory: Optional[str] = "./data/chroma_db"
    
    # Weaviate特定配置
    weaviate_url: Optional[str] = None
    weaviate_api_key: Optional[str] = None
    
    # Pinecone特定配置
    pinecone_api_key: Optional[str] = None
    pinecone_environment: Optional[str] = None


@dataclass
class EmbeddingConfig:
    """嵌入模型配置"""
    model: EmbeddingModel = EmbeddingModel.QWEN_V3
    dimensions: int = 1536
    batch_size: int = 100
    max_retries: int = 3
    
    # API配置
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    
    # 本地模型配置
    model_path: Optional[str] = None
    device: str = "auto"  # auto, cpu, cuda


@dataclass
class RetrievalConfig:
    """检索配置"""
    strategy: RetrievalStrategy = RetrievalStrategy.HYBRID
    top_k: int = 10
    similarity_threshold: float = 0.7
    
    # 向量检索配置
    vector_top_k: int = 20
    vector_threshold: float = 0.6
    
    # 关键词检索配置
    keyword_top_k: int = 10
    keyword_analyzer: str = "standard"
    
    # 混合检索配置
    alpha: float = 0.7  # 向量检索权重
    beta: float = 0.3   # 关键词检索权重
    
    # 重排序配置
    enable_rerank: bool = True
    rerank_model: str = "cross-encoder"
    rerank_top_k: int = 5


@dataclass
class GenerationConfig:
    """生成配置"""
    max_context_length: int = 4000
    context_overlap: int = 200
    
    # Token管理
    max_tokens: int = 2000
    reserved_tokens: int = 500
    
    # 质量控制
    enable_hallucination_detection: bool = True
    enable_fact_checking: bool = True
    enable_citation_generation: bool = True
    
    # 提示模板
    system_prompt_template: str = "default"
    user_prompt_template: str = "default"


@dataclass
class CacheConfig:
    """缓存配置"""
    enable_cache: bool = True
    cache_type: str = "redis"  # redis, memory, disk
    
    # Redis配置
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 1
    redis_password: Optional[str] = None
    
    # 缓存策略
    query_cache_ttl: int = 3600  # 查询缓存1小时
    embedding_cache_ttl: int = 86400  # 嵌入缓存24小时
    result_cache_ttl: int = 1800  # 结果缓存30分钟


@dataclass
class MonitoringConfig:
    """监控配置"""
    enable_monitoring: bool = True
    metrics_port: int = 8080
    
    # 性能指标
    track_latency: bool = True
    track_throughput: bool = True
    track_accuracy: bool = True
    
    # 日志配置
    log_level: str = "INFO"
    log_file: Optional[str] = "./logs/rag.log"
    
    # 告警配置
    alert_threshold_latency: float = 1.0  # 1秒
    alert_threshold_error_rate: float = 0.05  # 5%


class RAGConfig:
    """RAG系统主配置类"""
    
    def __init__(self):
        # 从环境变量加载配置
        self.vector_store = VectorStoreConfig(
            provider=os.getenv("RAG_VECTOR_STORE", "chroma"),
            host=os.getenv("RAG_VECTOR_HOST", "localhost"),
            port=int(os.getenv("RAG_VECTOR_PORT", "8000")),
            collection_name=os.getenv("RAG_COLLECTION_NAME", "contract_knowledge"),
            persist_directory=os.getenv("RAG_PERSIST_DIR", "./data/chroma_db")
        )
        
        self.embedding = EmbeddingConfig(
            model=EmbeddingModel(os.getenv("RAG_EMBEDDING_MODEL", "text-embedding-v3")),
            dimensions=int(os.getenv("RAG_EMBEDDING_DIMS", "1536")),
            batch_size=int(os.getenv("RAG_EMBEDDING_BATCH_SIZE", "100")),
            api_key=os.getenv("DASHSCOPE_API_KEY")
        )
        
        self.retrieval = RetrievalConfig(
            strategy=RetrievalStrategy(os.getenv("RAG_RETRIEVAL_STRATEGY", "hybrid")),
            top_k=int(os.getenv("RAG_TOP_K", "10")),
            similarity_threshold=float(os.getenv("RAG_SIMILARITY_THRESHOLD", "0.7")),
            enable_rerank=os.getenv("RAG_ENABLE_RERANK", "true").lower() == "true"
        )
        
        self.generation = GenerationConfig(
            max_context_length=int(os.getenv("RAG_MAX_CONTEXT_LENGTH", "4000")),
            max_tokens=int(os.getenv("RAG_MAX_TOKENS", "2000")),
            enable_hallucination_detection=os.getenv("RAG_ENABLE_HALLUCINATION_DETECTION", "true").lower() == "true",
            enable_citation_generation=os.getenv("RAG_ENABLE_CITATION", "true").lower() == "true"
        )
        
        self.cache = CacheConfig(
            enable_cache=os.getenv("RAG_ENABLE_CACHE", "true").lower() == "true",
            cache_type=os.getenv("RAG_CACHE_TYPE", "redis"),
            redis_host=os.getenv("REDIS_HOST", "localhost"),
            redis_port=int(os.getenv("REDIS_PORT", "6379")),
            redis_password=os.getenv("REDIS_PASSWORD")
        )
        
        self.monitoring = MonitoringConfig(
            enable_monitoring=os.getenv("RAG_ENABLE_MONITORING", "true").lower() == "true",
            log_level=os.getenv("RAG_LOG_LEVEL", "INFO"),
            log_file=os.getenv("RAG_LOG_FILE", "./logs/rag.log")
        )
    
    def validate(self) -> bool:
        """验证配置有效性"""
        try:
            # 验证必需的配置项
            if not self.embedding.api_key and self.embedding.model == EmbeddingModel.QWEN_V3:
                raise ValueError("DASHSCOPE_API_KEY is required for Qwen embedding model")
            
            if self.retrieval.top_k <= 0:
                raise ValueError("top_k must be positive")
            
            if self.generation.max_context_length <= 0:
                raise ValueError("max_context_length must be positive")
            
            return True
            
        except Exception as e:
            print(f"配置验证失败: {e}")
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "vector_store": self.vector_store.__dict__,
            "embedding": self.embedding.__dict__,
            "retrieval": self.retrieval.__dict__,
            "generation": self.generation.__dict__,
            "cache": self.cache.__dict__,
            "monitoring": self.monitoring.__dict__
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "RAGConfig":
        """从字典创建配置"""
        config = cls()
        
        if "vector_store" in config_dict:
            config.vector_store = VectorStoreConfig(**config_dict["vector_store"])
        
        if "embedding" in config_dict:
            config.embedding = EmbeddingConfig(**config_dict["embedding"])
        
        if "retrieval" in config_dict:
            config.retrieval = RetrievalConfig(**config_dict["retrieval"])
        
        if "generation" in config_dict:
            config.generation = GenerationConfig(**config_dict["generation"])
        
        if "cache" in config_dict:
            config.cache = CacheConfig(**config_dict["cache"])
        
        if "monitoring" in config_dict:
            config.monitoring = MonitoringConfig(**config_dict["monitoring"])
        
        return config


# 全局配置实例
rag_config = RAGConfig()
