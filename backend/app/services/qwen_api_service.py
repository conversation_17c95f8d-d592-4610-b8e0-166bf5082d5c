"""
千问API服务模块
支持阿里云百炼平台的千问模型API调用
"""

import os
import asyncio
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

import dashscope
from openai import OpenAI
from langchain_community.llms import Tongyi
from langchain_community.embeddings import DashScopeEmbeddings

from app.core.config import settings

logger = logging.getLogger(__name__)


class QwenModelType(Enum):
    """千问模型类型枚举"""
    MAX = "qwen-max"          # 最强效果，复杂任务
    PLUS = "qwen-plus"        # 均衡选择，推荐
    FLASH = "qwen-flash"      # 高速低成本
    LONG = "qwen-long"        # 长文本处理


@dataclass
class QwenResponse:
    """千问API响应数据类"""
    content: str
    model: str
    usage: Dict[str, int]
    finish_reason: str
    error: Optional[str] = None


class QwenAPIService:
    """千问API服务类"""
    
    def __init__(self, api_key: Optional[str] = None):
        """初始化千问API服务"""
        self.api_key = api_key or settings.DASHSCOPE_API_KEY
        if not self.api_key:
            raise ValueError("DASHSCOPE_API_KEY is required")
        
        # 配置DashScope
        dashscope.api_key = self.api_key
        
        # 配置OpenAI兼容客户端
        self.openai_client = OpenAI(
            api_key=self.api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )
        
        # 任务模型映射
        self.task_model_mapping = {
            "summary": QwenModelType.FLASH,      # 摘要生成 - 快速
            "risk": QwenModelType.PLUS,          # 风险评估 - 均衡
            "clause": QwenModelType.PLUS,        # 条款分析 - 均衡
            "compliance": QwenModelType.MAX,     # 合规检查 - 最强
            "long_doc": QwenModelType.LONG,      # 长文档 - 专用
            "general": QwenModelType.PLUS        # 通用任务 - 均衡
        }
        
        # 初始化嵌入模型
        self.embeddings = DashScopeEmbeddings(
            model="text-embedding-v3",
            dashscope_api_key=self.api_key
        )
    
    def get_model_for_task(self, task_type: str) -> QwenModelType:
        """根据任务类型获取最适合的模型"""
        return self.task_model_mapping.get(task_type, QwenModelType.PLUS)
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        task_type: str = "general",
        **kwargs
    ) -> QwenResponse:
        """聊天完成API调用"""
        try:
            model = self.get_model_for_task(task_type)
            
            response = self.openai_client.chat.completions.create(
                model=model.value,
                messages=messages,
                temperature=kwargs.get("temperature", 0.1),
                max_tokens=kwargs.get("max_tokens", 2000),
                top_p=kwargs.get("top_p", 0.8),
                stream=False
            )
            
            return QwenResponse(
                content=response.choices[0].message.content,
                model=model.value,
                usage=response.usage.model_dump(),
                finish_reason=response.choices[0].finish_reason
            )
            
        except Exception as e:
            logger.error(f"千问API调用失败: {str(e)}")
            return QwenResponse(
                content="",
                model=model.value,
                usage={},
                finish_reason="error",
                error=str(e)
            )
    
    async def generate_contract_summary(self, contract_text: str) -> str:
        """生成合同摘要"""
        messages = [
            {
                "role": "system",
                "content": "你是一位专业的法律顾问，擅长分析合同文档。请为用户提供的合同生成简洁准确的摘要。"
            },
            {
                "role": "user",
                "content": f"""
请为以下合同生成一份简洁的摘要，包括：
1. 合同性质和目的
2. 主要当事方
3. 核心条款
4. 重要时间节点
5. 主要权利义务

合同内容：
{contract_text}

请用专业但易懂的语言，控制在300字以内：
"""
            }
        ]
        
        response = await self.chat_completion(messages, task_type="summary")
        return response.content if not response.error else f"生成摘要失败: {response.error}"
    
    async def assess_contract_risks(self, contract_text: str) -> Dict[str, Any]:
        """评估合同风险"""
        messages = [
            {
                "role": "system",
                "content": "你是一位专业的法律风险评估师，擅长识别合同中的各种风险。请提供详细的风险分析。"
            },
            {
                "role": "user",
                "content": f"""
请对以下合同进行全面的风险评估，从以下维度分析：

1. 法律风险 - 条款是否符合法律法规
2. 商业风险 - 商业条款是否合理
3. 操作风险 - 执行过程中可能遇到的问题
4. 财务风险 - 付款、担保等财务相关风险
5. 合规风险 - 是否符合行业规范

对每个风险类别，请提供：
- 风险等级（低/中/高/严重）
- 风险描述
- 可能的影响
- 建议的缓解措施

合同内容：
{contract_text}

请以JSON格式返回结果。
"""
            }
        ]
        
        response = await self.chat_completion(messages, task_type="risk")
        
        if response.error:
            return {"error": f"风险评估失败: {response.error}"}
        
        try:
            import json
            return json.loads(response.content)
        except json.JSONDecodeError:
            return {
                "error": "解析风险评估结果失败",
                "raw_response": response.content
            }
    
    async def analyze_contract_clauses(self, contract_text: str) -> List[Dict[str, Any]]:
        """分析合同条款"""
        messages = [
            {
                "role": "system",
                "content": "你是一位专业的合同条款分析师，擅长逐条分析合同条款的完整性和合理性。"
            },
            {
                "role": "user",
                "content": f"""
请分析以下合同的条款，并提供详细的分析结果：

分析要点：
1. 条款类型识别
2. 条款完整性评估
3. 条款清晰度评估
4. 潜在问题识别
5. 改进建议

合同内容：
{contract_text}

请以JSON数组格式返回结果，每个条款一个对象。
"""
            }
        ]
        
        response = await self.chat_completion(messages, task_type="clause")
        
        if response.error:
            return [{"error": f"条款分析失败: {response.error}"}]
        
        try:
            import json
            return json.loads(response.content)
        except json.JSONDecodeError:
            return [{
                "error": "解析条款分析结果失败",
                "raw_response": response.content
            }]
    
    async def check_compliance(self, contract_text: str) -> Dict[str, Any]:
        """检查合规性"""
        messages = [
            {
                "role": "system",
                "content": "你是一位专业的法律合规专家，擅长检查合同的法律法规符合性。"
            },
            {
                "role": "user",
                "content": f"""
请检查以下合同的合规性，重点关注：

1. 法律法规符合性
2. 行业规范符合性
3. 强制性条款检查
4. 禁止性条款识别
5. 合规风险评估

合同内容：
{contract_text}

请以JSON格式返回检查结果，包括合规等级、问题清单和改进建议。
"""
            }
        ]
        
        response = await self.chat_completion(messages, task_type="compliance")
        
        if response.error:
            return {"error": f"合规检查失败: {response.error}"}
        
        try:
            import json
            return json.loads(response.content)
        except json.JSONDecodeError:
            return {
                "error": "解析合规检查结果失败",
                "raw_response": response.content
            }
    
    async def parallel_analysis(self, contract_text: str) -> Dict[str, Any]:
        """并行执行所有分析任务"""
        tasks = [
            self.generate_contract_summary(contract_text),
            self.assess_contract_risks(contract_text),
            self.analyze_contract_clauses(contract_text),
            self.check_compliance(contract_text)
        ]
        
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            return {
                "summary": results[0] if not isinstance(results[0], Exception) else str(results[0]),
                "risks": results[1] if not isinstance(results[1], Exception) else {"error": str(results[1])},
                "clauses": results[2] if not isinstance(results[2], Exception) else [{"error": str(results[2])}],
                "compliance": results[3] if not isinstance(results[3], Exception) else {"error": str(results[3])},
                "status": "completed"
            }
        except Exception as e:
            logger.error(f"并行分析失败: {str(e)}")
            return {
                "error": f"分析失败: {str(e)}",
                "status": "failed"
            }
    
    async def embed_texts(self, texts: List[str]) -> List[List[float]]:
        """文本向量化"""
        try:
            embeddings = await self.embeddings.aembed_documents(texts)
            return embeddings
        except Exception as e:
            logger.error(f"文本向量化失败: {str(e)}")
            return []
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """获取使用统计"""
        # 这里可以添加使用统计逻辑
        return {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_tokens": 0
        }


# 全局服务实例
qwen_service = QwenAPIService()
