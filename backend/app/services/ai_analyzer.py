"""
AI分析服务 - 负责合同内容的智能分析
"""
from typing import Dict, List, Optional, Tuple
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain.prompts import ChatPromptTemplate
from langchain.schema import HumanMessage, SystemMessage
from langchain_community.vectorstores import Chroma
from langchain.chains import RetrievalQA
import json
import asyncio
from loguru import logger
from dataclasses import dataclass

from app.core.config import settings
from app.models.contract import RiskLevel


@dataclass
class AnalysisResult:
    """分析结果数据类"""
    summary: str
    key_terms: Dict
    risk_assessment: Dict
    clause_analysis: List[Dict]
    compliance_check: Dict
    recommendations: List[str]


class AIAnalyzer:
    """AI合同分析器"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            temperature=0.1,
            max_tokens=settings.MAX_TOKENS,
            openai_api_key=settings.OPENAI_API_KEY,
            base_url=settings.OPENAI_BASE_URL
        )
        
        self.embeddings = OpenAIEmbeddings(
            openai_api_key=settings.OPENAI_API_KEY,
            base_url=settings.OPENAI_BASE_URL
        )
        
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=settings.CHUNK_SIZE,
            chunk_overlap=settings.CHUNK_OVERLAP,
            separators=["\n\n", "\n", "。", ".", " ", ""]
        )
    
    async def analyze_contract(self, text: str, contract_type: str = "通用合同") -> AnalysisResult:
        """
        全面分析合同
        
        Args:
            text: 合同文本
            contract_type: 合同类型
            
        Returns:
            AnalysisResult: 分析结果
        """
        logger.info(f"开始分析合同，类型: {contract_type}")
        
        # 并行执行各种分析
        tasks = [
            self._generate_summary(text, contract_type),
            self._extract_key_terms(text),
            self._assess_risks(text, contract_type),
            self._analyze_clauses(text),
            self._check_compliance(text, contract_type),
            self._generate_recommendations(text, contract_type)
        ]
        
        results = await asyncio.gather(*tasks)
        
        return AnalysisResult(
            summary=results[0],
            key_terms=results[1],
            risk_assessment=results[2],
            clause_analysis=results[3],
            compliance_check=results[4],
            recommendations=results[5]
        )
    
    async def _generate_summary(self, text: str, contract_type: str) -> str:
        """生成合同摘要"""
        prompt = ChatPromptTemplate.from_messages([
            SystemMessage(content=f"""
你是一位专业的法律顾问，请为以下{contract_type}生成一份简洁的摘要。

摘要应包括：
1. 合同的主要目的和性质
2. 合同当事方
3. 关键条款和义务
4. 重要时间节点
5. 主要权利和责任

请用专业但易懂的语言，控制在300字以内。
            """),
            HumanMessage(content=f"合同内容：\n{text[:3000]}")
        ])
        
        response = await self.llm.ainvoke(prompt.format_messages())
        return response.content
    
    async def _extract_key_terms(self, text: str) -> Dict:
        """提取关键条款"""
        prompt = ChatPromptTemplate.from_messages([
            SystemMessage(content="""
请从合同中提取以下关键信息，以JSON格式返回：

{
    "parties": ["甲方名称", "乙方名称"],
    "contract_value": "合同金额",
    "contract_date": "签署日期",
    "effective_date": "生效日期",
    "expiry_date": "到期日期",
    "payment_terms": "付款条款",
    "delivery_terms": "交付条款",
    "liability_terms": "责任条款",
    "termination_terms": "终止条款",
    "dispute_resolution": "争议解决方式"
}

如果某项信息不存在，请返回null。
            """),
            HumanMessage(content=f"合同内容：\n{text[:4000]}")
        ])
        
        response = await self.llm.ainvoke(prompt.format_messages())
        
        try:
            return json.loads(response.content)
        except json.JSONDecodeError:
            logger.warning("关键条款提取结果不是有效的JSON格式")
            return {}
    
    async def _assess_risks(self, text: str, contract_type: str) -> Dict:
        """风险评估"""
        prompt = ChatPromptTemplate.from_messages([
            SystemMessage(content=f"""
作为专业的法律风险评估师，请对以下{contract_type}进行全面的风险评估。

请从以下维度分析风险：
1. 法律风险 - 条款是否符合法律法规
2. 商业风险 - 商业条款是否合理
3. 操作风险 - 执行过程中可能遇到的问题
4. 财务风险 - 付款、担保等财务相关风险
5. 合规风险 - 是否符合行业规范

对每个风险类别，请提供：
- 风险等级（low/medium/high/critical）
- 风险描述
- 可能的影响
- 建议的缓解措施

请以JSON格式返回结果。
            """),
            HumanMessage(content=f"合同内容：\n{text[:4000]}")
        ])
        
        response = await self.llm.ainvoke(prompt.format_messages())
        
        try:
            risk_data = json.loads(response.content)
            
            # 计算整体风险评分
            risk_scores = {
                'low': 0.2,
                'medium': 0.5,
                'high': 0.8,
                'critical': 1.0
            }
            
            total_score = 0
            risk_count = 0
            
            for category, risk_info in risk_data.items():
                if isinstance(risk_info, dict) and 'level' in risk_info:
                    level = risk_info['level'].lower()
                    if level in risk_scores:
                        total_score += risk_scores[level]
                        risk_count += 1
            
            overall_score = total_score / risk_count if risk_count > 0 else 0
            
            # 确定整体风险等级
            if overall_score >= 0.8:
                overall_level = RiskLevel.CRITICAL
            elif overall_score >= 0.6:
                overall_level = RiskLevel.HIGH
            elif overall_score >= 0.3:
                overall_level = RiskLevel.MEDIUM
            else:
                overall_level = RiskLevel.LOW
            
            risk_data['overall_score'] = overall_score
            risk_data['overall_level'] = overall_level.value
            
            return risk_data
            
        except json.JSONDecodeError:
            logger.warning("风险评估结果不是有效的JSON格式")
            return {
                'overall_score': 0.5,
                'overall_level': RiskLevel.MEDIUM.value
            }
    
    async def _analyze_clauses(self, text: str) -> List[Dict]:
        """条款分析"""
        # 将文本分割成段落
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
        
        clause_analyses = []
        
        # 分析前10个重要段落
        for i, paragraph in enumerate(paragraphs[:10]):
            if len(paragraph) < 50:  # 跳过太短的段落
                continue
                
            prompt = ChatPromptTemplate.from_messages([
                SystemMessage(content="""
请分析以下合同条款，并以JSON格式返回分析结果：

{
    "clause_type": "条款类型（如：付款条款、责任条款等）",
    "is_standard": true/false,
    "is_favorable": true/false/null,
    "completeness_score": 0.0-1.0,
    "clarity_score": 0.0-1.0,
    "issues": ["发现的问题1", "问题2"],
    "suggestions": ["建议1", "建议2"]
}
                """),
                HumanMessage(content=f"条款内容：\n{paragraph}")
            ])
            
            try:
                response = await self.llm.ainvoke(prompt.format_messages())
                analysis = json.loads(response.content)
                analysis['clause_content'] = paragraph[:200] + "..." if len(paragraph) > 200 else paragraph
                analysis['position'] = f"第{i+1}段"
                clause_analyses.append(analysis)
            except Exception as e:
                logger.warning(f"条款分析失败: {e}")
                continue
        
        return clause_analyses
    
    async def _check_compliance(self, text: str, contract_type: str) -> Dict:
        """合规性检查"""
        prompt = ChatPromptTemplate.from_messages([
            SystemMessage(content=f"""
请对以下{contract_type}进行合规性检查，重点关注：

1. 《合同法》相关规定
2. 行业特定法规
3. 格式条款的合规性
4. 必备条款的完整性

请以JSON格式返回检查结果：
{{
    "overall_compliance": "compliant/non_compliant/partial",
    "compliance_score": 0.0-1.0,
    "checks": [
        {{
            "regulation": "相关法规名称",
            "status": "compliant/violation/warning",
            "description": "检查说明",
            "recommendation": "建议"
        }}
    ]
}}
            """),
            HumanMessage(content=f"合同内容：\n{text[:4000]}")
        ])
        
        response = await self.llm.ainvoke(prompt.format_messages())
        
        try:
            return json.loads(response.content)
        except json.JSONDecodeError:
            logger.warning("合规性检查结果不是有效的JSON格式")
            return {
                "overall_compliance": "partial",
                "compliance_score": 0.7,
                "checks": []
            }
    
    async def _generate_recommendations(self, text: str, contract_type: str) -> List[str]:
        """生成改进建议"""
        prompt = ChatPromptTemplate.from_messages([
            SystemMessage(content=f"""
基于对{contract_type}的分析，请提供具体的改进建议。

建议应该：
1. 具体可操作
2. 有助于降低风险
3. 提高合同的完整性和清晰度
4. 符合法律法规要求

请提供5-10条建议，每条建议简洁明了。
            """),
            HumanMessage(content=f"合同内容：\n{text[:3000]}")
        ])
        
        response = await self.llm.ainvoke(prompt.format_messages())
        
        # 将建议分割成列表
        recommendations = [
            rec.strip() 
            for rec in response.content.split('\n') 
            if rec.strip() and not rec.strip().startswith('#')
        ]
        
        return recommendations[:10]  # 限制建议数量
