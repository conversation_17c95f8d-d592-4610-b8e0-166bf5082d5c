#!/bin/bash

# AI合同审查系统 - 快速启动脚本
# 支持 Linux/macOS 系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_error "$1 未安装，请先安装 $1"
        exit 1
    fi
}

# 检查系统要求
check_requirements() {
    print_header "检查系统要求"
    
    # 检查Python
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
        print_message "Python版本: $(python3 --version)"
        
        # 检查Python版本是否 >= 3.11
        if [[ $(echo "$PYTHON_VERSION >= 3.11" | bc -l) -eq 0 ]]; then
            print_error "需要Python 3.11或更高版本"
            exit 1
        fi
    else
        print_error "Python3未安装"
        exit 1
    fi
    
    # 检查Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        print_message "Node.js版本: $(node --version)"
        
        if [[ $NODE_VERSION -lt 18 ]]; then
            print_error "需要Node.js 18或更高版本"
            exit 1
        fi
    else
        print_error "Node.js未安装"
        exit 1
    fi
    
    # 检查Docker
    check_command "docker"
    check_command "docker-compose"
    
    print_message "系统要求检查通过 ✓"
}

# 设置环境变量
setup_environment() {
    print_header "设置环境变量"
    
    if [[ ! -f .env ]]; then
        print_message "复制环境变量模板..."
        cp .env.example .env
        print_warning "请编辑 .env 文件，配置必要的环境变量（如OpenAI API Key）"
        print_message "环境变量文件已创建: .env"
    else
        print_message "环境变量文件已存在: .env"
    fi
}

# 创建必要的目录
create_directories() {
    print_header "创建项目目录"
    
    directories=("uploads" "logs" "database/backups")
    
    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            print_message "创建目录: $dir"
        fi
    done
}

# 设置后端环境
setup_backend() {
    print_header "设置后端环境"
    
    cd backend
    
    # 创建虚拟环境
    if [[ ! -d "venv" ]]; then
        print_message "创建Python虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    print_message "激活虚拟环境..."
    source venv/bin/activate
    
    # 升级pip
    print_message "升级pip..."
    pip install --upgrade pip
    
    # 安装依赖
    print_message "安装Python依赖..."
    pip install -r requirements.txt
    
    print_message "后端环境设置完成 ✓"
    cd ..
}

# 设置前端环境
setup_frontend() {
    print_header "设置前端环境"
    
    cd frontend
    
    # 安装依赖
    print_message "安装Node.js依赖..."
    npm install
    
    print_message "前端环境设置完成 ✓"
    cd ..
}

# Docker设置
setup_docker() {
    print_header "Docker环境设置"
    
    # 检查Docker是否运行
    if ! docker info &> /dev/null; then
        print_error "Docker未运行，请启动Docker服务"
        exit 1
    fi
    
    # 构建镜像
    print_message "构建Docker镜像..."
    docker-compose build
    
    print_message "Docker环境设置完成 ✓"
}

# 初始化数据库
init_database() {
    print_header "初始化数据库"
    
    # 启动数据库服务
    print_message "启动数据库服务..."
    docker-compose up -d postgres redis chroma
    
    # 等待数据库启动
    print_message "等待数据库启动..."
    sleep 10
    
    # 运行数据库迁移
    print_message "运行数据库迁移..."
    cd backend
    source venv/bin/activate
    
    # 检查alembic是否已初始化
    if [[ ! -d "alembic" ]]; then
        print_message "初始化Alembic..."
        alembic init alembic
    fi
    
    # 运行迁移
    alembic upgrade head
    
    print_message "数据库初始化完成 ✓"
    cd ..
}

# 启动服务
start_services() {
    print_header "启动服务"
    
    print_message "启动所有服务..."
    docker-compose up -d
    
    # 等待服务启动
    print_message "等待服务启动..."
    sleep 15
    
    # 检查服务状态
    print_message "检查服务状态..."
    docker-compose ps
    
    print_message "服务启动完成 ✓"
}

# 验证安装
verify_installation() {
    print_header "验证安装"
    
    # 检查后端API
    print_message "检查后端API..."
    if curl -f http://localhost:8000/health &> /dev/null; then
        print_message "后端API运行正常 ✓"
    else
        print_warning "后端API可能未正常启动"
    fi
    
    # 检查前端
    print_message "检查前端服务..."
    if curl -f http://localhost:3000 &> /dev/null; then
        print_message "前端服务运行正常 ✓"
    else
        print_warning "前端服务可能未正常启动"
    fi
    
    print_message "验证完成"
}

# 显示访问信息
show_access_info() {
    print_header "访问信息"
    
    echo -e "${GREEN}🎉 AI合同审查系统安装完成！${NC}"
    echo ""
    echo -e "${BLUE}访问地址:${NC}"
    echo -e "  前端应用: ${GREEN}http://localhost:3000${NC}"
    echo -e "  后端API: ${GREEN}http://localhost:8000${NC}"
    echo -e "  API文档: ${GREEN}http://localhost:8000/docs${NC}"
    echo ""
    echo -e "${BLUE}常用命令:${NC}"
    echo -e "  查看服务状态: ${YELLOW}docker-compose ps${NC}"
    echo -e "  查看日志: ${YELLOW}docker-compose logs -f${NC}"
    echo -e "  停止服务: ${YELLOW}docker-compose down${NC}"
    echo -e "  重启服务: ${YELLOW}docker-compose restart${NC}"
    echo ""
    echo -e "${YELLOW}注意事项:${NC}"
    echo -e "  1. 请确保在 .env 文件中配置了正确的 OpenAI API Key"
    echo -e "  2. 首次使用前，建议查看项目文档了解详细功能"
    echo -e "  3. 如遇问题，请查看日志文件或提交Issue"
}

# 主函数
main() {
    print_header "AI合同审查系统 - 自动安装脚本"
    
    # 检查是否在项目根目录
    if [[ ! -f "docker-compose.yml" ]]; then
        print_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行安装步骤
    check_requirements
    setup_environment
    create_directories
    setup_backend
    setup_frontend
    setup_docker
    init_database
    start_services
    verify_installation
    show_access_info
    
    print_message "安装完成！"
}

# 运行主函数
main "$@"
