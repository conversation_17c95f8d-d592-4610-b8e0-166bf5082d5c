# AI合同审查系统 - 环境变量配置模板

# ===========================================
# 基础应用配置
# ===========================================
PROJECT_NAME=AI合同审查系统
VERSION=1.0.0
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=your-secret-key-change-in-production

# ===========================================
# 数据库配置
# ===========================================
# PostgreSQL主数据库
DATABASE_URL=postgresql://contract_user:contract_pass@localhost:5432/contract_db

# Redis缓存数据库
REDIS_URL=redis://localhost:6379/0

# ===========================================
# AI模型配置
# ===========================================
# OpenAI配置（开发环境）
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_BASE_URL=https://api.openai.com/v1

# 千问API配置（推荐）
DASHSCOPE_API_KEY=your-dashscope-api-key-here
QWEN_API_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# 千问商业版模型配置
QWEN_MODEL_MAX=qwen-max          # 最强效果，复杂任务
QWEN_MODEL_PLUS=qwen-plus        # 均衡选择，推荐
QWEN_MODEL_FLASH=qwen-flash      # 高速低成本
QWEN_MODEL_LONG=qwen-long        # 长文本处理

# 本地模型配置（生产环境）
LOCAL_MODEL_ENABLED=false
LOCAL_MODEL_URL=http://localhost:11434

# 千问开源模型配置（本地部署）
QWEN_LOCAL_7B=qwen2.5:7b
QWEN_LOCAL_14B=qwen2.5:14b
QWEN_LOCAL_32B=qwen2.5:32b

# Llama模型配置
LLAMA_MODEL_8B=llama3:8b
LLAMA_MODEL_70B=llama3:70b

# 模型任务分配（API模式）
MODEL_SUMMARY=qwen-flash         # 摘要生成
MODEL_RISK=qwen-plus            # 风险评估
MODEL_CLAUSE=qwen-plus          # 条款分析
MODEL_COMPLIANCE=qwen-max       # 合规检查
MODEL_LONG_DOC=qwen-long        # 长文档处理

# 嵌入模型配置
EMBEDDING_MODEL=text-embedding-v3
EMBEDDING_DIMENSIONS=1536
EMBEDDING_BATCH_SIZE=100

# 模型切换配置
USE_API_MODE=true               # true: API调用, false: 本地部署
FALLBACK_MODEL=qwen-plus        # 备用模型

# ===========================================
# 向量数据库配置
# ===========================================
CHROMA_HOST=localhost
CHROMA_PORT=8001
CHROMA_COLLECTION_NAME=contract_knowledge

# ===========================================
# 文件上传配置
# ===========================================
UPLOAD_DIR=uploads
MAX_FILE_SIZE=52428800
ALLOWED_FILE_TYPES=.pdf,.docx,.doc,.txt

# ===========================================
# 安全配置
# ===========================================
ACCESS_TOKEN_EXPIRE_MINUTES=10080
ALLOWED_HOSTS=["*"]

# ===========================================
# 分析配置
# ===========================================
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_TOKENS=4000

# 风险评估阈值
RISK_THRESHOLD_HIGH=0.8
RISK_THRESHOLD_MEDIUM=0.5

# ===========================================
# 缓存配置
# ===========================================
CACHE_TTL=3600

# ===========================================
# 日志配置
# ===========================================
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# ===========================================
# 前端配置
# ===========================================
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_TITLE=AI合同审查系统
