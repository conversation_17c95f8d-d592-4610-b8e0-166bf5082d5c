<template>
  <div class="rag-config">
    <div class="rag-config__header">
      <h2>RAG系统配置</h2>
      <p class="rag-config__description">
        配置RAG系统的后端参数，包括检索策略、生成参数、模型设置等
      </p>
    </div>

    <el-form
      ref="formRef"
      :model="configForm"
      :rules="formRules"
      label-width="150px"
      class="rag-config__form"
    >
      <!-- 基础配置 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Setting /></el-icon>
            <span>基础配置</span>
          </div>
        </template>

        <el-form-item label="后端API地址" prop="apiBaseUrl">
          <el-input
            v-model="configForm.apiBaseUrl"
            placeholder="http://localhost:8000"
            clearable
          >
            <template #prepend>HTTP</template>
          </el-input>
        </el-form-item>

        <el-form-item label="WebSocket地址" prop="wsUrl">
          <el-input
            v-model="configForm.wsUrl"
            placeholder="ws://localhost:8000/ws"
            clearable
          >
            <template #prepend>WS</template>
          </el-input>
        </el-form-item>

        <el-form-item label="请求超时时间" prop="timeout">
          <el-input-number
            v-model="configForm.timeout"
            :min="5000"
            :max="300000"
            :step="1000"
            controls-position="right"
          />
          <span class="form-unit">毫秒</span>
        </el-form-item>

        <el-form-item label="启用调试模式" prop="enableDebug">
          <el-switch
            v-model="configForm.enableDebug"
            active-text="开启"
            inactive-text="关闭"
          />
        </el-form-item>
      </el-card>

      <!-- 检索配置 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Search /></el-icon>
            <span>检索配置</span>
          </div>
        </template>

        <el-form-item label="检索策略" prop="retrievalStrategy">
          <el-select
            v-model="configForm.retrievalStrategy"
            placeholder="请选择检索策略"
            style="width: 100%"
          >
            <el-option
              label="向量检索"
              value="vector_only"
              :disabled="false"
            />
            <el-option
              label="关键词检索"
              value="keyword_only"
              :disabled="false"
            />
            <el-option
              label="混合检索"
              value="hybrid"
              :disabled="false"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="检索数量(Top-K)" prop="topK">
          <el-input-number
            v-model="configForm.topK"
            :min="1"
            :max="50"
            :step="1"
            controls-position="right"
          />
          <span class="form-unit">个结果</span>
        </el-form-item>

        <el-form-item label="相似度阈值" prop="similarityThreshold">
          <el-slider
            v-model="configForm.similarityThreshold"
            :min="0"
            :max="1"
            :step="0.01"
            :format-tooltip="formatSimilarityTooltip"
            show-input
            :show-input-controls="false"
          />
        </el-form-item>

        <el-form-item label="启用重排序" prop="enableRerank">
          <el-switch
            v-model="configForm.enableRerank"
            active-text="开启"
            inactive-text="关闭"
          />
        </el-form-item>

        <el-form-item label="重排序模型" prop="rerankModel" v-if="configForm.enableRerank">
          <el-select
            v-model="configForm.rerankModel"
            placeholder="请选择重排序模型"
            style="width: 100%"
          >
            <el-option
              label="BGE Reranker"
              value="bge-reranker-base"
            />
            <el-option
              label="Cross Encoder"
              value="cross-encoder"
            />
          </el-select>
        </el-form-item>
      </el-card>

      <!-- 生成配置 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><ChatDotRound /></el-icon>
            <span>生成配置</span>
          </div>
        </template>

        <el-form-item label="语言模型" prop="llmModel">
          <el-select
            v-model="configForm.llmModel"
            placeholder="请选择语言模型"
            style="width: 100%"
          >
            <el-option
              label="GPT-4 Turbo"
              value="gpt-4-turbo"
            />
            <el-option
              label="GPT-3.5 Turbo"
              value="gpt-3.5-turbo"
            />
            <el-option
              label="千问2.5-7B"
              value="qwen2.5-7b"
            />
            <el-option
              label="千问2.5-14B"
              value="qwen2.5-14b"
            />
            <el-option
              label="Llama3-8B"
              value="llama3-8b"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="最大上下文长度" prop="maxContextLength">
          <el-input-number
            v-model="configForm.maxContextLength"
            :min="1000"
            :max="32000"
            :step="1000"
            controls-position="right"
          />
          <span class="form-unit">tokens</span>
        </el-form-item>

        <el-form-item label="最大生成长度" prop="maxTokens">
          <el-input-number
            v-model="configForm.maxTokens"
            :min="100"
            :max="4000"
            :step="100"
            controls-position="right"
          />
          <span class="form-unit">tokens</span>
        </el-form-item>

        <el-form-item label="温度参数" prop="temperature">
          <el-slider
            v-model="configForm.temperature"
            :min="0"
            :max="2"
            :step="0.1"
            :format-tooltip="formatTemperatureTooltip"
            show-input
            :show-input-controls="false"
          />
        </el-form-item>

        <el-form-item label="启用流式输出" prop="enableStreaming">
          <el-switch
            v-model="configForm.enableStreaming"
            active-text="开启"
            inactive-text="关闭"
          />
        </el-form-item>

        <el-form-item label="启用引用生成" prop="enableCitation">
          <el-switch
            v-model="configForm.enableCitation"
            active-text="开启"
            inactive-text="关闭"
          />
        </el-form-item>
      </el-card>

      <!-- 质量控制 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Shield /></el-icon>
            <span>质量控制</span>
          </div>
        </template>

        <el-form-item label="启用幻觉检测" prop="enableHallucinationDetection">
          <el-switch
            v-model="configForm.enableHallucinationDetection"
            active-text="开启"
            inactive-text="关闭"
          />
        </el-form-item>

        <el-form-item label="启用事实检查" prop="enableFactCheck">
          <el-switch
            v-model="configForm.enableFactCheck"
            active-text="开启"
            inactive-text="关闭"
          />
        </el-form-item>

        <el-form-item label="质量评分阈值" prop="qualityThreshold">
          <el-slider
            v-model="configForm.qualityThreshold"
            :min="0"
            :max="1"
            :step="0.01"
            :format-tooltip="formatQualityTooltip"
            show-input
            :show-input-controls="false"
          />
        </el-form-item>
      </el-card>

      <!-- 缓存配置 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><DataBoard /></el-icon>
            <span>缓存配置</span>
          </div>
        </template>

        <el-form-item label="启用查询缓存" prop="enableQueryCache">
          <el-switch
            v-model="configForm.enableQueryCache"
            active-text="开启"
            inactive-text="关闭"
          />
        </el-form-item>

        <el-form-item label="缓存过期时间" prop="cacheExpiration" v-if="configForm.enableQueryCache">
          <el-input-number
            v-model="configForm.cacheExpiration"
            :min="60"
            :max="86400"
            :step="60"
            controls-position="right"
          />
          <span class="form-unit">秒</span>
        </el-form-item>

        <el-form-item label="启用结果缓存" prop="enableResultCache">
          <el-switch
            v-model="configForm.enableResultCache"
            active-text="开启"
            inactive-text="关闭"
          />
        </el-form-item>
      </el-card>

      <!-- 操作按钮 -->
      <div class="rag-config__actions">
        <el-button @click="resetForm">重置</el-button>
        <el-button @click="testConnection" :loading="testing">测试连接</el-button>
        <el-button type="primary" @click="saveConfig" :loading="saving">
          保存配置
        </el-button>
      </div>
    </el-form>

    <!-- 连接状态 -->
    <el-card class="config-section" shadow="never" v-if="connectionStatus">
      <template #header>
        <div class="section-header">
          <el-icon><Connection /></el-icon>
          <span>连接状态</span>
        </div>
      </template>
      
      <div class="connection-status">
        <div class="status-item">
          <span class="status-label">API连接:</span>
          <el-tag :type="connectionStatus.api ? 'success' : 'danger'">
            {{ connectionStatus.api ? '正常' : '异常' }}
          </el-tag>
        </div>
        <div class="status-item">
          <span class="status-label">WebSocket连接:</span>
          <el-tag :type="connectionStatus.websocket ? 'success' : 'danger'">
            {{ connectionStatus.websocket ? '正常' : '异常' }}
          </el-tag>
        </div>
        <div class="status-item">
          <span class="status-label">模型状态:</span>
          <el-tag :type="connectionStatus.model ? 'success' : 'warning'">
            {{ connectionStatus.model ? '可用' : '不可用' }}
          </el-tag>
        </div>
        <div class="status-item">
          <span class="status-label">最后检测:</span>
          <span class="status-time">{{ connectionStatus.lastCheck }}</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Setting,
  Search,
  ChatDotRound,
  Shield,
  DataBoard,
  Connection
} from '@element-plus/icons-vue'
import { useRAGConfigStore } from '@/stores/ragConfig'
import { useSystemStore } from '@/stores/system'
import type { FormInstance, FormRules } from 'element-plus'

// Store
const ragConfigStore = useRAGConfigStore()
const systemStore = useSystemStore()

// 表单引用
const formRef = ref<FormInstance>()

// 响应式数据
const saving = ref(false)
const testing = ref(false)
const connectionStatus = ref<any>(null)

// 表单数据
const configForm = reactive({
  // 基础配置
  apiBaseUrl: 'http://localhost:8000',
  wsUrl: 'ws://localhost:8000/ws',
  timeout: 30000,
  enableDebug: false,
  
  // 检索配置
  retrievalStrategy: 'hybrid',
  topK: 10,
  similarityThreshold: 0.7,
  enableRerank: true,
  rerankModel: 'bge-reranker-base',
  
  // 生成配置
  llmModel: 'qwen2.5-7b',
  maxContextLength: 4000,
  maxTokens: 1000,
  temperature: 0.7,
  enableStreaming: true,
  enableCitation: true,
  
  // 质量控制
  enableHallucinationDetection: true,
  enableFactCheck: true,
  qualityThreshold: 0.8,
  
  // 缓存配置
  enableQueryCache: true,
  cacheExpiration: 3600,
  enableResultCache: true
})

// 表单验证规则
const formRules: FormRules = {
  apiBaseUrl: [
    { required: true, message: '请输入后端API地址', trigger: 'blur' },
    { pattern: /^https?:\/\/.+/, message: '请输入有效的URL地址', trigger: 'blur' }
  ],
  wsUrl: [
    { required: true, message: '请输入WebSocket地址', trigger: 'blur' },
    { pattern: /^wss?:\/\/.+/, message: '请输入有效的WebSocket地址', trigger: 'blur' }
  ],
  timeout: [
    { required: true, message: '请设置请求超时时间', trigger: 'blur' }
  ],
  retrievalStrategy: [
    { required: true, message: '请选择检索策略', trigger: 'change' }
  ],
  llmModel: [
    { required: true, message: '请选择语言模型', trigger: 'change' }
  ]
}

// 方法
const formatSimilarityTooltip = (value: number) => `${(value * 100).toFixed(0)}%`
const formatTemperatureTooltip = (value: number) => `${value.toFixed(1)} (${getTemperatureDesc(value)})`
const formatQualityTooltip = (value: number) => `${(value * 100).toFixed(0)}%`

const getTemperatureDesc = (temp: number) => {
  if (temp <= 0.3) return '保守'
  if (temp <= 0.7) return '平衡'
  if (temp <= 1.2) return '创新'
  return '随机'
}

const loadConfig = async () => {
  try {
    const config = await ragConfigStore.getConfig()
    Object.assign(configForm, config)
  } catch (error) {
    console.error('加载配置失败:', error)
  }
}

const saveConfig = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    saving.value = true
    
    await ragConfigStore.saveConfig(configForm)
    ElMessage.success('配置保存成功')
    
    // 重新加载系统状态
    await systemStore.refreshStatus()
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    saving.value = false
  }
}

const testConnection = async () => {
  testing.value = true
  
  try {
    const status = await ragConfigStore.testConnection(configForm)
    connectionStatus.value = {
      ...status,
      lastCheck: new Date().toLocaleString()
    }
    
    if (status.api && status.websocket) {
      ElMessage.success('连接测试成功')
    } else {
      ElMessage.warning('部分连接异常，请检查配置')
    }
  } catch (error) {
    console.error('连接测试失败:', error)
    ElMessage.error('连接测试失败')
  } finally {
    testing.value = false
  }
}

const resetForm = async () => {
  try {
    await ElMessageBox.confirm('确定要重置所有配置吗？', '确认重置', {
      type: 'warning'
    })
    
    if (formRef.value) {
      formRef.value.resetFields()
    }
    
    // 重置为默认值
    await ragConfigStore.resetConfig()
    await loadConfig()
    
    ElMessage.success('配置已重置')
  } catch (error) {
    // 用户取消操作
  }
}

// 生命周期
onMounted(() => {
  loadConfig()
})
</script>

<style lang="scss" scoped>
.rag-config {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  
  &__header {
    margin-bottom: 24px;
    
    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
  
  &__description {
    margin: 0;
    color: var(--el-text-color-secondary);
    font-size: 14px;
  }
  
  &__form {
    .config-section {
      margin-bottom: 24px;
      
      .section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        
        .el-icon {
          color: var(--el-color-primary);
        }
      }
    }
    
    .form-unit {
      margin-left: 8px;
      color: var(--el-text-color-secondary);
      font-size: 12px;
    }
  }
  
  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 0;
    border-top: 1px solid var(--el-border-color-lighter);
  }
}

.connection-status {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  
  .status-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    
    .status-label {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
    
    .status-time {
      color: var(--el-text-color-secondary);
      font-size: 12px;
    }
  }
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-slider) {
  margin: 0 12px;
}
</style>
