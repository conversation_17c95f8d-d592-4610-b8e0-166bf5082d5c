# RAG前端系统 - 组件库设计

## 1. 组件库概述

### 1.1 设计理念
RAG前端组件库专门为检索增强生成（RAG）系统设计，提供一套完整的、可复用的UI组件，支持智能问答、文档管理、检索可视化等RAG特有功能。

### 1.2 组件分类
- **通用组件**: 基础UI组件，可在任何场景使用
- **业务组件**: RAG业务相关的专用组件
- **布局组件**: 页面布局和导航组件
- **可视化组件**: 数据可视化和图表组件

### 1.3 设计原则
- **一致性**: 统一的视觉风格和交互模式
- **可复用性**: 组件高度可配置和可复用
- **可访问性**: 支持键盘导航和屏幕阅读器
- **响应式**: 适配不同屏幕尺寸
- **性能优化**: 懒加载和虚拟滚动

## 2. 通用组件

### 2.1 基础组件

#### BaseButton 按钮组件
```vue
<template>
  <button
    :class="buttonClass"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <el-icon v-if="loading" class="is-loading">
      <Loading />
    </el-icon>
    <el-icon v-else-if="icon">
      <component :is="icon" />
    </el-icon>
    <span v-if="$slots.default">
      <slot />
    </span>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElIcon, Loading } from 'element-plus'

interface Props {
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'
  size?: 'large' | 'default' | 'small'
  disabled?: boolean
  loading?: boolean
  icon?: string
  round?: boolean
  circle?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  size: 'default',
  disabled: false,
  loading: false,
  round: false,
  circle: false
})

interface Emits {
  (e: 'click', event: MouseEvent): void
}

const emit = defineEmits<Emits>()

const buttonClass = computed(() => [
  'base-button',
  `base-button--${props.type}`,
  `base-button--${props.size}`,
  {
    'is-disabled': props.disabled,
    'is-loading': props.loading,
    'is-round': props.round,
    'is-circle': props.circle
  }
])

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style lang="scss" scoped>
.base-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px 16px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  background-color: var(--el-bg-color);
  color: var(--el-text-color-primary);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover:not(.is-disabled) {
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
  }
  
  &--primary {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
    color: white;
    
    &:hover:not(.is-disabled) {
      background-color: var(--el-color-primary-light-3);
      border-color: var(--el-color-primary-light-3);
    }
  }
  
  &--large {
    padding: 12px 20px;
    font-size: 16px;
  }
  
  &--small {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  &.is-disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &.is-loading {
    pointer-events: none;
  }
  
  &.is-round {
    border-radius: 20px;
  }
  
  &.is-circle {
    border-radius: 50%;
    padding: 8px;
  }
}
</style>
```

#### BaseModal 模态框组件
```vue
<template>
  <Teleport to="body">
    <Transition name="modal">
      <div v-if="visible" class="base-modal-overlay" @click="handleOverlayClick">
        <div
          class="base-modal"
          :class="modalClass"
          @click.stop
        >
          <!-- 头部 -->
          <div v-if="showHeader" class="base-modal__header">
            <slot name="header">
              <h3 class="base-modal__title">{{ title }}</h3>
            </slot>
            <button
              v-if="showClose"
              class="base-modal__close"
              @click="handleClose"
            >
              <el-icon><Close /></el-icon>
            </button>
          </div>
          
          <!-- 内容 -->
          <div class="base-modal__body">
            <slot />
          </div>
          
          <!-- 底部 -->
          <div v-if="showFooter" class="base-modal__footer">
            <slot name="footer">
              <BaseButton @click="handleClose">取消</BaseButton>
              <BaseButton type="primary" @click="handleConfirm">确定</BaseButton>
            </slot>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElIcon, Close } from 'element-plus'
import BaseButton from './BaseButton.vue'

interface Props {
  visible: boolean
  title?: string
  width?: string | number
  showHeader?: boolean
  showFooter?: boolean
  showClose?: boolean
  closeOnClickModal?: boolean
  destroyOnClose?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  width: '50%',
  showHeader: true,
  showFooter: true,
  showClose: true,
  closeOnClickModal: true,
  destroyOnClose: false
})

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
  (e: 'confirm'): void
}

const emit = defineEmits<Emits>()

const modalClass = computed(() => ({
  [`base-modal--${typeof props.width === 'number' ? props.width + 'px' : props.width}`]: true
}))

const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

const handleConfirm = () => {
  emit('confirm')
}

const handleOverlayClick = () => {
  if (props.closeOnClickModal) {
    handleClose()
  }
}
</script>

<style lang="scss" scoped>
.base-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.base-modal {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid var(--el-border-color);
  }
  
  &__title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }
  
  &__close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    
    &:hover {
      background-color: var(--el-fill-color-light);
    }
  }
  
  &__body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }
  
  &__footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 16px 20px;
    border-top: 1px solid var(--el-border-color);
  }
}

.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}
</style>
```

## 3. RAG业务组件

### 3.1 文档管理组件

#### DocumentUploader 文档上传组件
```vue
<template>
  <div class="document-uploader">
    <div
      class="document-uploader__drop-zone"
      :class="{ 'is-dragover': isDragover }"
      @drop="handleDrop"
      @dragover="handleDragover"
      @dragleave="handleDragleave"
      @click="triggerFileInput"
    >
      <input
        ref="fileInputRef"
        type="file"
        :accept="acceptedTypes"
        :multiple="multiple"
        style="display: none"
        @change="handleFileSelect"
      >
      
      <div class="document-uploader__content">
        <el-icon class="document-uploader__icon">
          <Upload />
        </el-icon>
        <p class="document-uploader__text">
          拖拽文件到此处，或<span class="document-uploader__link">点击上传</span>
        </p>
        <p class="document-uploader__tip">
          支持 {{ acceptedTypes }} 格式，单个文件不超过 {{ maxSize }}MB
        </p>
      </div>
    </div>
    
    <!-- 上传列表 -->
    <div v-if="fileList.length > 0" class="document-uploader__list">
      <div
        v-for="file in fileList"
        :key="file.id"
        class="document-uploader__item"
      >
        <div class="document-uploader__item-info">
          <el-icon><Document /></el-icon>
          <span class="document-uploader__item-name">{{ file.name }}</span>
          <span class="document-uploader__item-size">{{ formatFileSize(file.size) }}</span>
        </div>
        
        <div class="document-uploader__item-progress">
          <el-progress
            :percentage="file.progress"
            :status="file.status"
            :stroke-width="4"
          />
        </div>
        
        <div class="document-uploader__item-actions">
          <el-button
            v-if="file.status === 'exception'"
            type="text"
            size="small"
            @click="retryUpload(file)"
          >
            重试
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="removeFile(file)"
          >
            删除
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElIcon, ElProgress, ElButton, Upload, Document } from 'element-plus'
import { useDocumentUpload } from '@/composables/useDocumentUpload'
import type { UploadFile } from '@/types/upload'

interface Props {
  accept?: string[]
  maxSize?: number
  multiple?: boolean
  autoUpload?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  accept: () => ['pdf', 'docx', 'doc', 'txt'],
  maxSize: 100,
  multiple: true,
  autoUpload: true
})

interface Emits {
  (e: 'success', file: UploadFile): void
  (e: 'error', error: Error, file: UploadFile): void
  (e: 'progress', progress: number, file: UploadFile): void
}

const emit = defineEmits<Emits>()

// 组合式函数
const { uploadFile, fileList, removeFile } = useDocumentUpload()

// 响应式数据
const fileInputRef = ref<HTMLInputElement>()
const isDragover = ref(false)

// 计算属性
const acceptedTypes = computed(() => {
  return props.accept.map(type => `.${type}`).join(',')
})

// 方法
const triggerFileInput = () => {
  fileInputRef.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = Array.from(target.files || [])
  handleFiles(files)
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragover.value = false
  
  const files = Array.from(event.dataTransfer?.files || [])
  handleFiles(files)
}

const handleDragover = (event: DragEvent) => {
  event.preventDefault()
  isDragover.value = true
}

const handleDragleave = () => {
  isDragover.value = false
}

const handleFiles = async (files: File[]) => {
  for (const file of files) {
    if (validateFile(file)) {
      if (props.autoUpload) {
        await uploadFile(file, {
          onProgress: (progress) => emit('progress', progress, file),
          onSuccess: (result) => emit('success', result),
          onError: (error) => emit('error', error, file)
        })
      }
    }
  }
}

const validateFile = (file: File): boolean => {
  const extension = file.name.split('.').pop()?.toLowerCase()
  
  if (!extension || !props.accept.includes(extension)) {
    ElMessage.error(`不支持的文件格式: ${extension}`)
    return false
  }
  
  if (file.size > props.maxSize * 1024 * 1024) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB`)
    return false
  }
  
  return true
}

const formatFileSize = (size: number): string => {
  if (size < 1024) return `${size}B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
  return `${(size / (1024 * 1024)).toFixed(1)}MB`
}

const retryUpload = async (file: UploadFile) => {
  await uploadFile(file.raw, {
    onProgress: (progress) => emit('progress', progress, file),
    onSuccess: (result) => emit('success', result),
    onError: (error) => emit('error', error, file)
  })
}
</script>

<style lang="scss" scoped>
.document-uploader {
  &__drop-zone {
    border: 2px dashed var(--el-border-color);
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover,
    &.is-dragover {
      border-color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
    }
  }
  
  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }
  
  &__icon {
    font-size: 48px;
    color: var(--el-text-color-secondary);
  }
  
  &__text {
    margin: 0;
    color: var(--el-text-color-primary);
  }
  
  &__link {
    color: var(--el-color-primary);
    cursor: pointer;
  }
  
  &__tip {
    margin: 0;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
  
  &__list {
    margin-top: 16px;
  }
  
  &__item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  &__item-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
  }
  
  &__item-name {
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  &__item-size {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
  
  &__item-progress {
    width: 200px;
  }
  
  &__item-actions {
    display: flex;
    gap: 4px;
  }
}
</style>
```

### 3.2 查询组件

#### QueryInput 智能查询输入组件
```vue
<template>
  <div class="query-input">
    <div class="query-input__wrapper">
      <el-input
        v-model="inputValue"
        type="textarea"
        :placeholder="placeholder"
        :rows="rows"
        :maxlength="maxLength"
        :show-word-limit="showWordLimit"
        :disabled="disabled"
        class="query-input__textarea"
        @input="handleInput"
        @keydown="handleKeydown"
        @focus="handleFocus"
        @blur="handleBlur"
      />
      
      <!-- 查询建议 -->
      <div
        v-if="showSuggestions && suggestions.length > 0"
        class="query-input__suggestions"
      >
        <div
          v-for="(suggestion, index) in suggestions"
          :key="index"
          class="query-input__suggestion-item"
          :class="{ 'is-active': activeSuggestionIndex === index }"
          @click="selectSuggestion(suggestion)"
          @mouseenter="activeSuggestionIndex = index"
        >
          <el-icon><Search /></el-icon>
          <span>{{ suggestion.text }}</span>
          <span v-if="suggestion.count" class="query-input__suggestion-count">
            {{ suggestion.count }}
          </span>
        </div>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="query-input__actions">
      <el-button
        v-if="showClear && inputValue"
        type="text"
        @click="handleClear"
      >
        清空
      </el-button>
      <el-button
        type="primary"
        :loading="loading"
        :disabled="!canSubmit"
        @click="handleSubmit"
      >
        {{ loading ? '查询中...' : '查询' }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElInput, ElButton, ElIcon, Search } from 'element-plus'
import { useDebounceFn } from '@vueuse/core'
import type { QuerySuggestion } from '@/types/query'

interface Props {
  modelValue: string
  placeholder?: string
  rows?: number
  maxLength?: number
  showWordLimit?: boolean
  disabled?: boolean
  loading?: boolean
  suggestions?: QuerySuggestion[]
  showSuggestions?: boolean
  showClear?: boolean
  minLength?: number
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入您的问题...',
  rows: 3,
  maxLength: 1000,
  showWordLimit: true,
  disabled: false,
  loading: false,
  suggestions: () => [],
  showSuggestions: true,
  showClear: true,
  minLength: 1
})

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'submit', value: string): void
  (e: 'clear'): void
  (e: 'input', value: string): void
  (e: 'suggestion-request', value: string): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const inputValue = ref(props.modelValue)
const isFocused = ref(false)
const activeSuggestionIndex = ref(-1)

// 计算属性
const canSubmit = computed(() => {
  return inputValue.value.trim().length >= props.minLength && !props.loading
})

// 防抖的建议请求
const debouncedSuggestionRequest = useDebounceFn((value: string) => {
  if (value.trim().length >= 2) {
    emit('suggestion-request', value)
  }
}, 300)

// 监听器
watch(() => props.modelValue, (newValue) => {
  inputValue.value = newValue
})

watch(inputValue, (newValue) => {
  emit('update:modelValue', newValue)
})

// 方法
const handleInput = (value: string) => {
  emit('input', value)
  debouncedSuggestionRequest(value)
}

const handleKeydown = (event: KeyboardEvent) => {
  if (!props.showSuggestions || props.suggestions.length === 0) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      handleSubmit()
    }
    return
  }
  
  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      activeSuggestionIndex.value = Math.min(
        activeSuggestionIndex.value + 1,
        props.suggestions.length - 1
      )
      break
    case 'ArrowUp':
      event.preventDefault()
      activeSuggestionIndex.value = Math.max(
        activeSuggestionIndex.value - 1,
        -1
      )
      break
    case 'Enter':
      if (!event.shiftKey) {
        event.preventDefault()
        if (activeSuggestionIndex.value >= 0) {
          selectSuggestion(props.suggestions[activeSuggestionIndex.value])
        } else {
          handleSubmit()
        }
      }
      break
    case 'Escape':
      activeSuggestionIndex.value = -1
      break
  }
}

const handleFocus = () => {
  isFocused.value = true
}

const handleBlur = () => {
  // 延迟隐藏建议，以便点击建议项
  setTimeout(() => {
    isFocused.value = false
    activeSuggestionIndex.value = -1
  }, 200)
}

const handleSubmit = () => {
  if (canSubmit.value) {
    emit('submit', inputValue.value.trim())
  }
}

const handleClear = () => {
  inputValue.value = ''
  emit('clear')
  nextTick(() => {
    // 重新聚焦到输入框
    const textarea = document.querySelector('.query-input__textarea textarea') as HTMLTextAreaElement
    textarea?.focus()
  })
}

const selectSuggestion = (suggestion: QuerySuggestion) => {
  inputValue.value = suggestion.text
  activeSuggestionIndex.value = -1
  handleSubmit()
}

const showSuggestions = computed(() => {
  return props.showSuggestions && isFocused.value && props.suggestions.length > 0
})
</script>

<style lang="scss" scoped>
.query-input {
  position: relative;
  
  &__wrapper {
    position: relative;
  }
  
  &__textarea {
    :deep(.el-textarea__inner) {
      resize: none;
      border-radius: 8px;
      font-size: 14px;
      line-height: 1.5;
    }
  }
  
  &__suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
  }
  
  &__suggestion-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:hover,
    &.is-active {
      background-color: var(--el-fill-color-light);
    }
    
    .el-icon {
      color: var(--el-text-color-secondary);
    }
  }
  
  &__suggestion-count {
    margin-left: auto;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
  
  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 12px;
  }
}
</style>
```

---

**文档版本**: v1.0.0  
**最后更新**: 2024年  
**维护状态**: 活跃维护
