# RAG前端系统 - 技术选型与规范

## 1. 技术选型

### 1.1 核心技术栈

#### 前端框架
- **Vue 3.5+**: 采用Composition API，提供更好的TypeScript支持和逻辑复用
- **TypeScript 5.3+**: 提供完整的类型安全，减少运行时错误
- **Vite 7.0+**: 快速的开发构建工具，支持热更新和优化打包

#### UI组件库
- **Element Plus 2.11.1+**: 成熟的Vue 3组件库，提供丰富的RAG界面组件
- **@element-plus/icons-vue**: Element Plus官方图标库
- **自定义RAG组件**: 专门为RAG工作流设计的业务组件

#### 状态管理
- **Pinia 3.1+**: Vue 3官方推荐的状态管理库，支持TypeScript
- **持久化插件**: 支持状态持久化到localStorage

#### 路由管理
- **Vue Router 4.2+**: Vue 3官方路由库，支持动态路由和路由守卫

#### HTTP客户端
- **Axios 1.6+**: 功能强大的HTTP客户端，支持拦截器和请求/响应转换
- **Socket.IO Client**: WebSocket客户端，用于实时通信

#### 可视化库
- **ECharts 5.4+**: 强大的图表库，用于RAG性能可视化
- **Vue-ECharts 6.6+**: ECharts的Vue 3封装
- **D3.js**: 用于复杂的知识图谱可视化

#### 工具库
- **Lodash-ES**: 实用工具函数库（ES模块版本）
- **Day.js**: 轻量级日期处理库
- **File-Saver**: 文件下载工具
- **NProgress**: 页面加载进度条

### 1.2 开发工具

#### 代码质量
- **ESLint**: JavaScript/TypeScript代码检查
- **Prettier**: 代码格式化工具
- **Husky**: Git hooks管理
- **lint-staged**: 暂存文件检查

#### 构建工具
- **Vite**: 开发服务器和构建工具
- **Rollup**: 底层打包工具（Vite内置）
- **PostCSS**: CSS后处理器
- **Sass**: CSS预处理器

#### 测试工具
- **Vitest**: 基于Vite的单元测试框架
- **@vue/test-utils**: Vue组件测试工具
- **Cypress**: E2E测试框架
- **MSW**: Mock Service Worker，用于API模拟

## 2. 项目结构规范

### 2.1 目录命名规范

```
rag/frontend/
├── src/
│   ├── components/          # 组件目录（PascalCase）
│   │   ├── common/         # 通用组件（小写+连字符）
│   │   ├── document/       # 文档相关组件
│   │   └── query/          # 查询相关组件
│   ├── views/              # 页面视图（PascalCase）
│   ├── api/                # API接口（小写）
│   ├── stores/             # 状态管理（小写）
│   ├── utils/              # 工具函数（小写）
│   ├── types/              # 类型定义（小写）
│   ├── assets/             # 静态资源（小写）
│   └── composables/        # 组合式函数（小写）
```

### 2.2 文件命名规范

#### Vue组件文件
- **页面组件**: PascalCase，如 `DocumentManager.vue`
- **业务组件**: PascalCase，如 `QueryInterface.vue`
- **通用组件**: PascalCase + 前缀，如 `BaseButton.vue`

#### TypeScript文件
- **API文件**: kebab-case，如 `document-api.ts`
- **工具文件**: kebab-case，如 `text-processing.ts`
- **类型文件**: kebab-case，如 `api-types.ts`
- **Store文件**: kebab-case，如 `document-store.ts`

#### 样式文件
- **全局样式**: kebab-case，如 `global-styles.scss`
- **组件样式**: 与组件同名，如 `DocumentManager.vue` → `DocumentManager.scss`

## 3. 编码规范

### 3.1 Vue 3 编码规范

#### 组件定义
```vue
<template>
  <!-- 使用kebab-case命名HTML属性 -->
  <div class="rag-query-interface">
    <el-input
      v-model="queryText"
      :placeholder="placeholder"
      @input="handleInput"
    />
  </div>
</template>

<script setup lang="ts">
// 导入顺序：第三方库 → 内部模块 → 相对路径
import { ref, computed, onMounted } from 'vue'
import { ElInput } from 'element-plus'
import { useQueryStore } from '@/stores/query'
import type { QueryParams } from '@/types/query'

// Props定义
interface Props {
  placeholder?: string
  maxLength?: number
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入查询内容...',
  maxLength: 1000
})

// Emits定义
interface Emits {
  (e: 'query', value: string): void
  (e: 'clear'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const queryText = ref<string>('')
const isLoading = ref<boolean>(false)

// 计算属性
const isQueryValid = computed(() => {
  return queryText.value.trim().length > 0
})

// 方法
const handleInput = (value: string) => {
  if (value.length <= props.maxLength) {
    queryText.value = value
  }
}

const handleQuery = () => {
  if (isQueryValid.value) {
    emit('query', queryText.value)
  }
}

// 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style lang="scss" scoped>
.rag-query-interface {
  // 使用BEM命名规范
  &__input {
    width: 100%;
  }
  
  &__button {
    margin-left: 8px;
  }
}
</style>
```

#### 组合式函数规范
```typescript
// composables/useRAGQuery.ts
import { ref, computed } from 'vue'
import type { QueryResult, QueryParams } from '@/types/query'

export function useRAGQuery() {
  // 状态
  const isLoading = ref<boolean>(false)
  const error = ref<string | null>(null)
  const results = ref<QueryResult[]>([])
  
  // 计算属性
  const hasResults = computed(() => results.value.length > 0)
  const hasError = computed(() => error.value !== null)
  
  // 方法
  const executeQuery = async (params: QueryParams) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await queryApi.search(params)
      results.value = response.results
    } catch (err) {
      error.value = err instanceof Error ? err.message : '查询失败'
    } finally {
      isLoading.value = false
    }
  }
  
  const clearResults = () => {
    results.value = []
    error.value = null
  }
  
  return {
    // 状态
    isLoading: readonly(isLoading),
    error: readonly(error),
    results: readonly(results),
    
    // 计算属性
    hasResults,
    hasError,
    
    // 方法
    executeQuery,
    clearResults
  }
}
```

### 3.2 TypeScript 编码规范

#### 类型定义
```typescript
// types/query.ts

// 接口命名使用PascalCase
export interface QueryParams {
  query: string
  filters?: QueryFilters
  pagination?: PaginationParams
}

export interface QueryFilters {
  documentType?: DocumentType[]
  dateRange?: DateRange
  relevanceThreshold?: number
}

// 类型别名使用PascalCase
export type DocumentType = 'pdf' | 'docx' | 'txt'
export type QueryStatus = 'idle' | 'loading' | 'success' | 'error'

// 枚举使用PascalCase
export enum RetrievalStrategy {
  VECTOR_ONLY = 'vector_only',
  KEYWORD_ONLY = 'keyword_only',
  HYBRID = 'hybrid'
}

// 常量使用SCREAMING_SNAKE_CASE
export const MAX_QUERY_LENGTH = 1000
export const DEFAULT_PAGE_SIZE = 20

// 泛型约束
export interface APIResponse<T = any> {
  success: boolean
  data: T
  message?: string
  timestamp: number
}

// 工具类型
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>
```

#### API服务规范
```typescript
// api/query.ts
import type { QueryParams, QueryResponse } from '@/types/query'
import { request } from '@/utils/request'

export class QueryAPI {
  private readonly baseURL = '/api/v1/query'
  
  /**
   * 执行RAG查询
   * @param params 查询参数
   * @returns 查询结果
   */
  async search(params: QueryParams): Promise<QueryResponse> {
    return request.post<QueryResponse>(`${this.baseURL}/search`, params)
  }
  
  /**
   * 获取查询建议
   * @param query 查询文本
   * @returns 建议列表
   */
  async getSuggestions(query: string): Promise<string[]> {
    return request.get<string[]>(`${this.baseURL}/suggestions`, {
      params: { q: query }
    })
  }
  
  /**
   * 获取查询历史
   * @param limit 限制数量
   * @returns 历史记录
   */
  async getHistory(limit = 10): Promise<QueryHistory[]> {
    return request.get<QueryHistory[]>(`${this.baseURL}/history`, {
      params: { limit }
    })
  }
}

export const queryApi = new QueryAPI()
```

### 3.3 样式编码规范

#### SCSS规范
```scss
// assets/styles/variables.scss
// 变量命名使用kebab-case
$rag-primary-color: #1890ff;
$rag-success-color: #52c41a;
$rag-warning-color: #faad14;
$rag-error-color: #f5222d;

// 间距变量
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 断点变量
$breakpoint-mobile: 768px;
$breakpoint-tablet: 1024px;
$breakpoint-desktop: 1440px;

// Mixin定义
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin respond-to($breakpoint) {
  @media (min-width: $breakpoint) {
    @content;
  }
}
```

#### BEM命名规范
```scss
// 组件样式使用BEM命名规范
.rag-query-interface {
  // Block
  padding: $spacing-md;
  
  // Element
  &__header {
    margin-bottom: $spacing-md;
    
    // Element的Element
    &-title {
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  &__input {
    width: 100%;
    margin-bottom: $spacing-sm;
  }
  
  &__button {
    // Modifier
    &--primary {
      background-color: $rag-primary-color;
    }
    
    &--loading {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
  
  // State classes
  &.is-loading {
    pointer-events: none;
  }
  
  &.has-error {
    border-color: $rag-error-color;
  }
}
```

## 4. Git提交规范

### 4.1 提交消息格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

#### Type类型
- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码格式调整（不影响功能）
- **refactor**: 代码重构
- **perf**: 性能优化
- **test**: 测试相关
- **chore**: 构建工具或辅助工具的变动
- **ci**: CI/CD相关变更

#### Scope范围
- **components**: 组件相关
- **views**: 页面相关
- **api**: API相关
- **stores**: 状态管理相关
- **utils**: 工具函数相关
- **types**: 类型定义相关
- **styles**: 样式相关

#### 示例
```
feat(components): 添加RAG查询界面组件

- 实现智能查询输入框
- 添加查询建议功能
- 支持查询历史记录

Closes #123
```

### 4.2 分支命名规范

- **主分支**: `main`
- **开发分支**: `develop`
- **功能分支**: `feature/功能名称`
- **修复分支**: `fix/问题描述`
- **发布分支**: `release/版本号`
- **热修复分支**: `hotfix/问题描述`

## 5. 代码质量保证

### 5.1 ESLint配置

```javascript
// .eslintrc.js
module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es2022: true
  },
  extends: [
    'eslint:recommended',
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  rules: {
    // Vue相关规则
    'vue/multi-word-component-names': 'error',
    'vue/component-definition-name-casing': ['error', 'PascalCase'],
    'vue/component-name-in-template-casing': ['error', 'kebab-case'],
    
    // TypeScript相关规则
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'warn',
    '@typescript-eslint/no-explicit-any': 'warn',
    
    // 通用规则
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'prefer-const': 'error',
    'no-var': 'error'
  }
}
```

### 5.2 Prettier配置

```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "none",
  "printWidth": 80,
  "bracketSpacing": true,
  "arrowParens": "avoid",
  "vueIndentScriptAndStyle": false
}
```

### 5.3 Husky配置

```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  },
  "lint-staged": {
    "*.{js,ts,vue}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{css,scss,vue}": [
      "stylelint --fix",
      "prettier --write"
    ]
  }
}
```

---

**文档版本**: v1.0.0  
**最后更新**: 2024年  
**维护状态**: 活跃维护
