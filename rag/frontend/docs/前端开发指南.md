# RAG前端系统 - 开发指南

## 1. 开发环境搭建

### 1.1 环境要求

#### 基础环境
- **Node.js**: 18.0+ (推荐使用LTS版本)
- **npm**: 9.0+ 或 **yarn**: 1.22+ 或 **pnpm**: 8.0+
- **Git**: 2.30+
- **现代浏览器**: Chrome 90+, Firefox 88+, Safari 14+

#### 开发工具推荐
- **IDE**: VS Code (推荐) 或 WebStorm
- **VS Code插件**:
  - Vue Language Features (Volar)
  - TypeScript Vue Plugin (Volar)
  - ESLint
  - Prettier
  - Auto Rename Tag
  - Bracket Pair Colorizer

### 1.2 项目初始化

#### 克隆项目
```bash
# 克隆项目
git clone <repository-url>
cd rag/frontend

# 检查Node.js版本
node --version  # 应该 >= 18.0.0
npm --version   # 应该 >= 9.0.0
```

#### 安装依赖
```bash
# 使用npm
npm install

# 使用yarn
yarn install

# 使用pnpm (推荐，更快的安装速度)
pnpm install
```

#### 环境配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

#### 环境变量配置说明
```bash
# .env 文件配置
# 应用基础配置
VITE_APP_TITLE=RAG智能分析系统
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=基于检索增强生成的智能问答系统

# API配置
VITE_API_BASE_URL=http://localhost:8000
VITE_API_TIMEOUT=30000
VITE_WS_URL=ws://localhost:8000/ws

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEBUG=true
VITE_ENABLE_DEVTOOLS=true

# 上传配置
VITE_UPLOAD_MAX_SIZE=100
VITE_UPLOAD_CHUNK_SIZE=2
VITE_UPLOAD_ALLOWED_TYPES=pdf,docx,doc,txt,md

# RAG配置
VITE_RAG_MAX_CONTEXT_LENGTH=4000
VITE_RAG_TOP_K=10
VITE_RAG_SIMILARITY_THRESHOLD=0.7

# 界面配置
VITE_DEFAULT_LANGUAGE=zh-CN
VITE_DEFAULT_THEME=light
VITE_ENABLE_DARK_MODE=true
```

### 1.3 开发服务器启动

```bash
# 启动开发服务器
npm run dev

# 或使用yarn
yarn dev

# 或使用pnpm
pnpm dev
```

访问地址：http://localhost:3000

## 2. 开发流程

### 2.1 功能开发流程

#### 1. 创建功能分支
```bash
# 从develop分支创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/rag-query-interface
```

#### 2. 开发前准备
- 阅读相关需求文档
- 了解RAG系统架构
- 确认API接口设计
- 准备测试数据

#### 3. 组件开发
```bash
# 创建组件文件
touch src/components/query/QueryInterface.vue
touch src/components/query/QueryInput.vue
touch src/components/query/QueryHistory.vue
```

#### 4. 类型定义
```typescript
// src/types/query.ts
export interface QueryRequest {
  query: string
  filters?: QueryFilters
  options?: QueryOptions
}

export interface QueryResponse {
  answer: string
  sources: Source[]
  metadata: QueryMetadata
}

export interface QueryFilters {
  documentTypes?: string[]
  dateRange?: DateRange
  relevanceThreshold?: number
}
```

#### 5. API服务开发
```typescript
// src/api/query.ts
import { request } from '@/utils/request'
import type { QueryRequest, QueryResponse } from '@/types/query'

export const queryApi = {
  async search(params: QueryRequest): Promise<QueryResponse> {
    return request.post('/api/v1/rag/query', params)
  },
  
  async getHistory(limit = 20): Promise<QueryHistory[]> {
    return request.get('/api/v1/query/history', { params: { limit } })
  }
}
```

#### 6. 状态管理
```typescript
// src/stores/query.ts
import { defineStore } from 'pinia'
import { queryApi } from '@/api/query'
import type { QueryRequest, QueryResponse } from '@/types/query'

export const useQueryStore = defineStore('query', () => {
  const currentQuery = ref<string>('')
  const isLoading = ref<boolean>(false)
  const results = ref<QueryResponse | null>(null)
  const history = ref<QueryHistory[]>([])
  
  const executeQuery = async (request: QueryRequest) => {
    isLoading.value = true
    try {
      const response = await queryApi.search(request)
      results.value = response
      await loadHistory() // 刷新历史记录
    } finally {
      isLoading.value = false
    }
  }
  
  const loadHistory = async () => {
    history.value = await queryApi.getHistory()
  }
  
  return {
    currentQuery,
    isLoading,
    results,
    history,
    executeQuery,
    loadHistory
  }
})
```

### 2.2 组件开发规范

#### Vue组件模板
```vue
<template>
  <div class="rag-query-interface">
    <!-- 查询输入区域 -->
    <div class="rag-query-interface__input-section">
      <QueryInput
        v-model:query="queryText"
        :loading="isLoading"
        :suggestions="suggestions"
        @submit="handleQuery"
        @clear="handleClear"
      />
    </div>
    
    <!-- 结果展示区域 -->
    <div class="rag-query-interface__results-section">
      <QueryResults
        v-if="results"
        :results="results"
        :loading="isLoading"
        @feedback="handleFeedback"
      />
    </div>
    
    <!-- 历史记录侧边栏 -->
    <div class="rag-query-interface__history-sidebar">
      <QueryHistory
        :history="history"
        @select="handleHistorySelect"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useQueryStore } from '@/stores/query'
import { useRAGQuery } from '@/composables/useRAGQuery'
import QueryInput from './QueryInput.vue'
import QueryResults from './QueryResults.vue'
import QueryHistory from './QueryHistory.vue'
import type { QueryRequest } from '@/types/query'

// 组合式函数
const queryStore = useQueryStore()
const { executeQuery, isLoading, results, suggestions } = useRAGQuery()

// 响应式数据
const queryText = ref<string>('')

// 计算属性
const history = computed(() => queryStore.history)

// 方法
const handleQuery = async () => {
  if (!queryText.value.trim()) return
  
  const request: QueryRequest = {
    query: queryText.value,
    filters: {
      relevanceThreshold: 0.7
    }
  }
  
  await executeQuery(request)
}

const handleClear = () => {
  queryText.value = ''
  results.value = null
}

const handleHistorySelect = (historyItem: QueryHistory) => {
  queryText.value = historyItem.query
  handleQuery()
}

const handleFeedback = (feedback: QueryFeedback) => {
  // 处理用户反馈
  console.log('用户反馈:', feedback)
}

// 生命周期
onMounted(() => {
  queryStore.loadHistory()
})
</script>

<style lang="scss" scoped>
.rag-query-interface {
  display: grid;
  grid-template-columns: 1fr 300px;
  grid-template-rows: auto 1fr;
  grid-template-areas:
    "input input"
    "results history";
  gap: 16px;
  height: 100vh;
  padding: 16px;
  
  &__input-section {
    grid-area: input;
  }
  
  &__results-section {
    grid-area: results;
    overflow-y: auto;
  }
  
  &__history-sidebar {
    grid-area: history;
    border-left: 1px solid var(--el-border-color);
    padding-left: 16px;
  }
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    grid-template-areas:
      "input"
      "results"
      "history";
  }
}
</style>
```

### 2.3 组合式函数开发

```typescript
// src/composables/useRAGQuery.ts
import { ref, computed } from 'vue'
import { useWebSocket } from './useWebSocket'
import { queryApi } from '@/api/query'
import type { QueryRequest, QueryResponse, QuerySuggestion } from '@/types/query'

export function useRAGQuery() {
  // 状态
  const isLoading = ref<boolean>(false)
  const error = ref<string | null>(null)
  const results = ref<QueryResponse | null>(null)
  const suggestions = ref<QuerySuggestion[]>([])
  const streamingAnswer = ref<string>('')
  
  // WebSocket连接
  const { socket, isConnected } = useWebSocket()
  
  // 计算属性
  const hasResults = computed(() => results.value !== null)
  const hasError = computed(() => error.value !== null)
  
  // 执行查询
  const executeQuery = async (request: QueryRequest) => {
    isLoading.value = true
    error.value = null
    streamingAnswer.value = ''
    
    try {
      // 如果支持流式响应，使用WebSocket
      if (isConnected.value) {
        await executeStreamingQuery(request)
      } else {
        // 否则使用HTTP请求
        const response = await queryApi.search(request)
        results.value = response
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '查询失败'
    } finally {
      isLoading.value = false
    }
  }
  
  // 流式查询
  const executeStreamingQuery = async (request: QueryRequest) => {
    return new Promise<void>((resolve, reject) => {
      socket.emit('rag_query', request)
      
      socket.on('rag_answer_chunk', (chunk: string) => {
        streamingAnswer.value += chunk
      })
      
      socket.on('rag_answer_complete', (response: QueryResponse) => {
        results.value = {
          ...response,
          answer: streamingAnswer.value
        }
        resolve()
      })
      
      socket.on('rag_error', (errorMsg: string) => {
        error.value = errorMsg
        reject(new Error(errorMsg))
      })
    })
  }
  
  // 获取查询建议
  const getSuggestions = async (query: string) => {
    if (query.length < 2) {
      suggestions.value = []
      return
    }
    
    try {
      const response = await queryApi.getSuggestions(query)
      suggestions.value = response
    } catch (err) {
      console.warn('获取查询建议失败:', err)
    }
  }
  
  // 清除结果
  const clearResults = () => {
    results.value = null
    error.value = null
    streamingAnswer.value = ''
  }
  
  return {
    // 状态
    isLoading: readonly(isLoading),
    error: readonly(error),
    results: readonly(results),
    suggestions: readonly(suggestions),
    streamingAnswer: readonly(streamingAnswer),
    
    // 计算属性
    hasResults,
    hasError,
    
    // 方法
    executeQuery,
    getSuggestions,
    clearResults
  }
}
```

## 3. 调试与测试

### 3.1 开发调试

#### Vue DevTools
```bash
# 安装Vue DevTools浏览器扩展
# Chrome: https://chrome.google.com/webstore/detail/vuejs-devtools/
# Firefox: https://addons.mozilla.org/en-US/firefox/addon/vue-js-devtools/
```

#### 调试技巧
```typescript
// 在组件中添加调试信息
import { getCurrentInstance } from 'vue'

export default {
  setup() {
    // 获取当前组件实例
    const instance = getCurrentInstance()
    console.log('组件实例:', instance)
    
    // 调试响应式数据
    const debugData = ref('test')
    watchEffect(() => {
      console.log('debugData变化:', debugData.value)
    })
    
    return { debugData }
  }
}
```

#### 网络请求调试
```typescript
// src/utils/request.ts
import axios from 'axios'

const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 30000
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    if (import.meta.env.VITE_ENABLE_DEBUG) {
      console.log('API请求:', config)
    }
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    if (import.meta.env.VITE_ENABLE_DEBUG) {
      console.log('API响应:', response)
    }
    return response.data
  },
  (error) => {
    console.error('响应错误:', error)
    return Promise.reject(error)
  }
)
```

### 3.2 单元测试

#### 测试配置
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./tests/setup.ts']
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  }
})
```

#### 组件测试示例
```typescript
// tests/components/QueryInput.test.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import QueryInput from '@/components/query/QueryInput.vue'

describe('QueryInput', () => {
  it('应该正确渲染查询输入框', () => {
    const wrapper = mount(QueryInput, {
      props: {
        placeholder: '请输入查询内容'
      }
    })
    
    expect(wrapper.find('input').exists()).toBe(true)
    expect(wrapper.find('input').attributes('placeholder')).toBe('请输入查询内容')
  })
  
  it('应该在输入时触发事件', async () => {
    const wrapper = mount(QueryInput)
    const input = wrapper.find('input')
    
    await input.setValue('测试查询')
    await input.trigger('input')
    
    expect(wrapper.emitted('update:query')).toBeTruthy()
    expect(wrapper.emitted('update:query')?.[0]).toEqual(['测试查询'])
  })
  
  it('应该在提交时触发查询事件', async () => {
    const wrapper = mount(QueryInput, {
      props: {
        query: '测试查询'
      }
    })
    
    await wrapper.find('form').trigger('submit')
    
    expect(wrapper.emitted('submit')).toBeTruthy()
  })
})
```

#### API测试示例
```typescript
// tests/api/query.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { queryApi } from '@/api/query'
import type { QueryRequest } from '@/types/query'

// Mock axios
vi.mock('@/utils/request', () => ({
  request: {
    post: vi.fn(),
    get: vi.fn()
  }
}))

describe('QueryAPI', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })
  
  it('应该正确发送查询请求', async () => {
    const mockResponse = {
      answer: '测试答案',
      sources: [],
      metadata: {}
    }
    
    const { request } = await import('@/utils/request')
    vi.mocked(request.post).mockResolvedValue(mockResponse)
    
    const queryRequest: QueryRequest = {
      query: '测试查询',
      filters: {
        relevanceThreshold: 0.7
      }
    }
    
    const result = await queryApi.search(queryRequest)
    
    expect(request.post).toHaveBeenCalledWith('/api/v1/rag/query', queryRequest)
    expect(result).toEqual(mockResponse)
  })
})
```

### 3.3 运行测试

```bash
# 运行所有测试
npm run test

# 运行测试并监听文件变化
npm run test:watch

# 运行测试并生成覆盖率报告
npm run test:coverage

# 运行特定测试文件
npm run test QueryInput.test.ts
```

## 4. 构建与部署

### 4.1 构建配置

```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 分析构建包大小
npm run build:analyze
```

### 4.2 部署准备

#### 环境变量配置
```bash
# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_WS_URL=wss://api.example.com/ws
VITE_ENABLE_DEBUG=false
VITE_ENABLE_DEVTOOLS=false
```

#### Nginx配置
```nginx
# nginx.conf
server {
    listen 80;
    server_name rag-frontend.example.com;
    
    root /usr/share/nginx/html;
    index index.html;
    
    # 处理Vue Router的history模式
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://rag-backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # WebSocket代理
    location /ws/ {
        proxy_pass http://rag-backend:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

---

**文档版本**: v1.0.0  
**最后更新**: 2024年  
**维护状态**: 活跃维护
