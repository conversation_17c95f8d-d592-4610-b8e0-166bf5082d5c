# RAG前端系统 - 架构设计

## 1. 架构概述

### 1.1 设计理念
RAG前端系统采用现代化的前端架构设计，专门为检索增强生成（RAG）工作流程优化，遵循组件化、模块化、可维护性的设计原则，为用户提供高效、直观的智能问答和知识检索体验。

### 1.2 核心原则
- **RAG工作流优化**: 专门为RAG检索-生成流程设计的用户界面
- **实时交互**: 支持流式问答和实时检索结果展示
- **可视化检索**: 检索过程透明化，结果可追溯
- **性能优化**: 针对大量文档和复杂查询的性能优化
- **用户体验**: 直观的RAG交互设计和智能反馈

## 2. 技术架构

### 2.1 RAG前端整体架构图

```mermaid
graph TB
    subgraph "用户交互层"
        A[Web浏览器] --> B[Vue 3 RAG应用]
    end
    
    subgraph "RAG前端应用层"
        B --> C[路由层 Vue Router]
        B --> D[RAG状态管理层 Pinia]
        B --> E[RAG组件层]
    end
    
    subgraph "RAG服务层"
        C --> F[RAG API服务层]
        D --> F
        E --> F
        F --> G[HTTP拦截器]
        F --> H[WebSocket服务]
        F --> I[文档上传服务]
    end
    
    subgraph "RAG工具层"
        F --> J[文本处理工具]
        E --> J
        J --> K[RAG类型定义]
        J --> L[RAG常量配置]
    end
    
    subgraph "RAG后端接口"
        G --> M[文档管理API]
        G --> N[检索API]
        G --> O[生成API]
        H --> P[实时问答WebSocket]
    end
```

### 2.2 RAG专用分层架构设计

#### 2.2.1 RAG交互层 (RAG Interaction Layer)
```typescript
// RAG页面组件结构
views/
├── Dashboard.vue            # RAG系统仪表板
│   ├── RAGMetrics.vue      # RAG性能指标
│   ├── RecentQueries.vue   # 最近查询
│   └── SystemStatus.vue    # 系统状态
├── DocumentManager.vue      # 文档管理页面
│   ├── DocumentUpload.vue  # 文档上传界面
│   ├── DocumentList.vue    # 文档列表管理
│   └── VectorStatus.vue    # 向量化状态
├── QueryInterface.vue       # 智能问答页面
│   ├── ChatInterface.vue   # 对话界面
│   ├── QueryInput.vue      # 查询输入
│   └── StreamingResponse.vue # 流式响应
├── KnowledgeBase.vue        # 知识库浏览页面
│   ├── KnowledgeGraph.vue  # 知识图谱
│   ├── CategoryBrowser.vue # 分类浏览
│   └── SearchInterface.vue # 搜索界面
└── Analytics.vue            # RAG分析统计页面
    ├── RetrievalAnalytics.vue # 检索分析
    ├── GenerationMetrics.vue  # 生成指标
    └── QualityMetrics.vue     # 质量指标
```

#### 2.2.2 RAG组件层 (RAG Component Layer)
```typescript
// RAG专用组件库结构
components/
├── common/                  # 通用组件
│   ├── RAGHeader.vue       # RAG系统头部
│   ├── RAGSidebar.vue      # RAG导航侧边栏
│   ├── LoadingSpinner.vue  # 加载动画
│   └── ErrorBoundary.vue   # 错误边界
├── document/                # 文档管理组件
│   ├── DocumentUploader.vue    # 文档上传器
│   ├── DocumentPreview.vue     # 文档预览器
│   ├── VectorizeProgress.vue   # 向量化进度
│   ├── DocumentMetadata.vue    # 文档元数据
│   └── BatchProcessor.vue      # 批量处理器
├── query/                   # 查询相关组件
│   ├── SmartQueryInput.vue     # 智能查询输入
│   ├── QuerySuggestions.vue    # 查询建议
│   ├── QueryHistory.vue        # 查询历史
│   ├── ContextManager.vue      # 上下文管理
│   └── QueryFilters.vue        # 查询过滤器
├── retrieval/               # 检索相关组件
│   ├── RetrievalResults.vue    # 检索结果展示
│   ├── RelevanceScore.vue      # 相关性评分
│   ├── RetrievalProcess.vue    # 检索过程可视化
│   ├── HybridSearch.vue        # 混合搜索界面
│   └── ResultReranking.vue     # 结果重排序
├── generation/              # 生成相关组件
│   ├── StreamingAnswer.vue     # 流式答案展示
│   ├── AnswerQuality.vue       # 答案质量评估
│   ├── CitationView.vue        # 引用展示
│   ├── FactChecking.vue        # 事实检查
│   └── AnswerFeedback.vue      # 答案反馈
└── visualization/           # 可视化组件
    ├── KnowledgeGraph.vue      # 知识图谱
    ├── RetrievalFlow.vue       # 检索流程图
    ├── PerformanceChart.vue    # 性能图表
    ├── QualityDashboard.vue    # 质量仪表板
    └── MetricsVisualization.vue # 指标可视化
```

#### 2.2.3 RAG状态管理层 (RAG State Layer)
```typescript
// RAG专用Pinia Store结构
stores/
├── modules/
│   ├── document.ts          # 文档管理状态
│   │   ├── documentList     # 文档列表
│   │   ├── uploadProgress   # 上传进度
│   │   ├── vectorizeStatus  # 向量化状态
│   │   └── documentMetadata # 文档元数据
│   ├── query.ts             # 查询状态
│   │   ├── currentQuery     # 当前查询
│   │   ├── queryHistory     # 查询历史
│   │   ├── queryContext     # 查询上下文
│   │   └── suggestions      # 查询建议
│   ├── retrieval.ts         # 检索状态
│   │   ├── retrievalResults # 检索结果
│   │   ├── relevanceScores  # 相关性评分
│   │   ├── retrievalMetrics # 检索指标
│   │   └── searchFilters    # 搜索过滤器
│   ├── generation.ts        # 生成状态
│   │   ├── generatedAnswer  # 生成的答案
│   │   ├── streamingStatus  # 流式状态
│   │   ├── answerQuality    # 答案质量
│   │   └── citations        # 引用信息
│   ├── knowledge.ts         # 知识库状态
│   │   ├── knowledgeGraph   # 知识图谱
│   │   ├── categories       # 分类信息
│   │   ├── statistics       # 统计数据
│   │   └── searchIndex      # 搜索索引
│   └── system.ts            # 系统状态
│       ├── ragMetrics       # RAG指标
│       ├── systemHealth     # 系统健康状态
│       ├── performanceData  # 性能数据
│       └── errorLogs        # 错误日志
└── index.ts                 # Store入口
```

#### 2.2.4 RAG服务层 (RAG Service Layer)
```typescript
// RAG API服务结构
services/
├── api/
│   ├── document.ts          # 文档管理API
│   │   ├── uploadDocument   # 上传文档
│   │   ├── getDocuments     # 获取文档列表
│   │   ├── deleteDocument   # 删除文档
│   │   └── getVectorStatus  # 获取向量化状态
│   ├── retrieval.ts         # 检索API
│   │   ├── vectorSearch     # 向量检索
│   │   ├── keywordSearch    # 关键词检索
│   │   ├── hybridSearch     # 混合检索
│   │   └── rerankResults    # 结果重排序
│   ├── generation.ts        # 生成API
│   │   ├── generateAnswer   # 生成答案
│   │   ├── streamGenerate   # 流式生成
│   │   ├── checkQuality     # 质量检查
│   │   └── getCitations     # 获取引用
│   ├── knowledge.ts         # 知识库API
│   │   ├── getKnowledgeGraph # 获取知识图谱
│   │   ├── getCategories    # 获取分类
│   │   ├── searchKnowledge  # 搜索知识
│   │   └── getStatistics    # 获取统计
│   └── analytics.ts         # 分析API
│       ├── getRAGMetrics    # 获取RAG指标
│       ├── getPerformance   # 获取性能数据
│       ├── getQualityMetrics # 获取质量指标
│       └── getUsageStats    # 获取使用统计
├── websocket.ts             # WebSocket服务
│   ├── connectRAGSocket     # 连接RAG WebSocket
│   ├── subscribeToAnswers   # 订阅答案流
│   ├── subscribeToProgress  # 订阅进度更新
│   └── handleRealtimeData   # 处理实时数据
├── upload.ts                # 文件上传服务
│   ├── uploadWithProgress   # 带进度的上传
│   ├── batchUpload          # 批量上传
│   ├── validateFile         # 文件验证
│   └── processDocument      # 文档处理
└── cache.ts                 # 缓存服务
    ├── queryCache           # 查询缓存
    ├── resultCache          # 结果缓存
    ├── documentCache        # 文档缓存
    └── clearCache           # 清除缓存
```

## 3. RAG核心模块设计

### 3.1 智能问答模块

```typescript
// 智能问答组合式函数
export function useRAGQuery() {
  const query = ref<string>('')
  const isLoading = ref(false)
  const streamingAnswer = ref<string>('')
  const retrievalResults = ref<RetrievalResult[]>([])
  const citations = ref<Citation[]>([])
  
  const executeRAGQuery = async (queryText: string) => {
    isLoading.value = true
    streamingAnswer.value = ''
    
    try {
      // 1. 执行检索
      const retrievalResponse = await retrievalApi.hybridSearch({
        query: queryText,
        top_k: 10,
        enable_rerank: true
      })
      retrievalResults.value = retrievalResponse.results
      
      // 2. 流式生成答案
      const socket = useWebSocket()
      socket.emit('generate_answer', {
        query: queryText,
        context: retrievalResponse.context
      })
      
      socket.on('answer_chunk', (chunk: string) => {
        streamingAnswer.value += chunk
      })
      
      socket.on('answer_complete', (data: AnswerComplete) => {
        citations.value = data.citations
        isLoading.value = false
      })
      
    } catch (error) {
      console.error('RAG查询失败:', error)
      isLoading.value = false
    }
  }
  
  return {
    query,
    isLoading,
    streamingAnswer,
    retrievalResults,
    citations,
    executeRAGQuery
  }
}
```

### 3.2 文档管理模块

```typescript
// 文档管理组合式函数
export function useDocumentManager() {
  const documents = ref<Document[]>([])
  const uploadProgress = ref<Map<string, number>>(new Map())
  const vectorizeStatus = ref<Map<string, VectorizeStatus>>(new Map())
  
  const uploadDocument = async (file: File) => {
    const fileId = generateId()
    uploadProgress.value.set(fileId, 0)
    
    try {
      // 上传文档
      const result = await documentApi.uploadDocument(file, {
        onUploadProgress: (progress) => {
          uploadProgress.value.set(fileId, progress.percent)
        }
      })
      
      // 监听向量化进度
      const socket = useWebSocket()
      socket.on(`vectorize_progress_${result.id}`, (status: VectorizeStatus) => {
        vectorizeStatus.value.set(result.id, status)
      })
      
      // 开始向量化
      await documentApi.startVectorization(result.id)
      
      return result
    } catch (error) {
      uploadProgress.value.delete(fileId)
      throw error
    }
  }
  
  const getDocuments = async () => {
    const response = await documentApi.getDocuments()
    documents.value = response.documents
  }
  
  return {
    documents,
    uploadProgress,
    vectorizeStatus,
    uploadDocument,
    getDocuments
  }
}
```

### 3.3 检索可视化模块

```typescript
// 检索可视化组合式函数
export function useRetrievalVisualization() {
  const retrievalFlow = ref<RetrievalStep[]>([])
  const relevanceScores = ref<RelevanceScore[]>([])
  const rerankingProcess = ref<RerankingStep[]>([])
  
  const visualizeRetrieval = async (query: string) => {
    // 获取检索流程数据
    const flowData = await retrievalApi.getRetrievalFlow(query)
    retrievalFlow.value = flowData.steps
    
    // 获取相关性评分
    const scoreData = await retrievalApi.getRelevanceScores(query)
    relevanceScores.value = scoreData.scores
    
    // 获取重排序过程
    const rerankData = await retrievalApi.getRerankingProcess(query)
    rerankingProcess.value = rerankData.steps
  }
  
  const renderRetrievalChart = () => {
    // 使用ECharts渲染检索流程图
    const chartOption = {
      title: { text: '检索流程可视化' },
      series: [{
        type: 'sankey',
        data: retrievalFlow.value.map(step => ({
          name: step.name,
          value: step.score
        })),
        links: retrievalFlow.value.map(step => ({
          source: step.source,
          target: step.target,
          value: step.weight
        }))
      }]
    }
    return chartOption
  }
  
  return {
    retrievalFlow,
    relevanceScores,
    rerankingProcess,
    visualizeRetrieval,
    renderRetrievalChart
  }
}
```

## 4. RAG数据流设计

### 4.1 RAG数据流架构图

```mermaid
graph LR
    subgraph "用户交互"
        A[用户查询] --> B[RAG查询组件]
    end
    
    subgraph "前端处理"
        B --> C[查询预处理]
        C --> D[Pinia状态更新]
    end
    
    subgraph "RAG API通信"
        D --> E[检索API调用]
        E --> F[生成API调用]
        F --> G[WebSocket流式响应]
    end
    
    subgraph "结果处理"
        G --> H[流式答案展示]
        E --> I[检索结果展示]
        F --> J[引用信息展示]
    end
    
    subgraph "状态同步"
        H --> D
        I --> D
        J --> D
    end
```

### 4.2 RAG API接口设计

```typescript
// RAG API接口类型定义
interface RAGAPI {
  // 文档管理接口
  uploadDocument(file: File, config?: UploadConfig): Promise<Document>
  getDocuments(params?: DocumentListParams): Promise<DocumentListResponse>
  deleteDocument(id: string): Promise<void>
  startVectorization(documentId: string): Promise<VectorizeTask>
  getVectorizeStatus(taskId: string): Promise<VectorizeStatus>
  
  // 检索接口
  vectorSearch(params: VectorSearchParams): Promise<RetrievalResponse>
  keywordSearch(params: KeywordSearchParams): Promise<RetrievalResponse>
  hybridSearch(params: HybridSearchParams): Promise<RetrievalResponse>
  rerankResults(params: RerankParams): Promise<RerankResponse>
  
  // 生成接口
  generateAnswer(params: GenerateParams): Promise<GenerateResponse>
  streamGenerate(params: StreamGenerateParams): Promise<ReadableStream>
  checkAnswerQuality(answer: string, context: string): Promise<QualityScore>
  getCitations(answer: string, context: string): Promise<Citation[]>
  
  // 知识库接口
  getKnowledgeGraph(params?: GraphParams): Promise<KnowledgeGraph>
  searchKnowledge(query: string, filters?: SearchFilters): Promise<KnowledgeSearchResponse>
  getKnowledgeStatistics(): Promise<KnowledgeStats>
  
  // 分析接口
  getRAGMetrics(timeRange?: TimeRange): Promise<RAGMetrics>
  getPerformanceData(timeRange?: TimeRange): Promise<PerformanceData>
  getQualityMetrics(timeRange?: TimeRange): Promise<QualityMetrics>
}
```

## 5. RAG UI/UX设计

### 5.1 RAG专用设计系统

```scss
// RAG设计令牌
:root {
  // RAG主色调
  --rag-primary: #1890ff;
  --rag-success: #52c41a;
  --rag-warning: #faad14;
  --rag-error: #f5222d;
  --rag-info: #722ed1;
  
  // RAG功能色
  --retrieval-color: #13c2c2;
  --generation-color: #eb2f96;
  --document-color: #fa8c16;
  --knowledge-color: #a0d911;
  
  // RAG语义色
  --relevance-high: #52c41a;
  --relevance-medium: #faad14;
  --relevance-low: #f5222d;
  --citation-color: #722ed1;
  --streaming-color: #1890ff;
  
  // RAG间距
  --rag-spacing-xs: 4px;
  --rag-spacing-sm: 8px;
  --rag-spacing-md: 16px;
  --rag-spacing-lg: 24px;
  --rag-spacing-xl: 32px;
}
```

### 5.2 RAG响应式设计

```scss
// RAG响应式断点
$rag-breakpoints: (
  mobile: 0,
  tablet: 768px,
  desktop: 1024px,
  large: 1440px
);

// RAG响应式混入
@mixin rag-respond-to($breakpoint) {
  @media (min-width: map-get($rag-breakpoints, $breakpoint)) {
    @content;
  }
}

// RAG布局适配
.rag-layout {
  display: grid;
  
  @include rag-respond-to(mobile) {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "header"
      "main"
      "footer";
  }
  
  @include rag-respond-to(tablet) {
    grid-template-columns: 250px 1fr;
    grid-template-areas: 
      "sidebar header"
      "sidebar main"
      "sidebar footer";
  }
  
  @include rag-respond-to(desktop) {
    grid-template-columns: 280px 1fr 320px;
    grid-template-areas: 
      "sidebar header aside"
      "sidebar main aside"
      "sidebar footer aside";
  }
}
```

---

**文档版本**: v1.0.0  
**最后更新**: 2024年  
**维护状态**: 活跃维护
