# RAG前端系统 - 文档中心

## 📚 文档导航

本目录包含RAG（检索增强生成）系统前端的完整技术文档，涵盖架构设计、开发指南、组件库等各个方面。

### 📋 文档结构

```
rag/frontend/docs/
├── README.md                    # 本文档 - 前端文档导航
├── 前端架构设计.md              # RAG前端整体架构设计
├── 前端技术选型与规范.md        # 技术栈选择和开发规范
├── 前端开发指南.md              # 开发环境搭建和开发流程
├── 组件库设计.md                # UI组件库设计和使用指南
├── 状态管理设计.md              # Pinia状态管理架构
├── 路由设计.md                  # Vue Router路由设计
├── API接口设计.md               # 前后端接口设计
├── 前端部署指南.md              # 构建和部署配置
└── 前端测试指南.md              # 测试策略和规范
```

## 🎯 RAG前端系统概述

### 系统定位
RAG前端系统是一个专门为检索增强生成（RAG）技术设计的现代化Web应用，为用户提供直观、高效的文档管理、智能问答和知识检索界面。

### 核心功能模块
- **文档管理**: 法律文档上传、预览、分类管理
- **智能问答**: 自然语言查询、实时问答交互
- **知识检索**: 向量检索、混合检索、结果展示
- **结果可视化**: 检索结果展示、引用溯源、相关性分析
- **知识库浏览**: 知识图谱可视化、分类浏览
- **分析报告**: 风险评估报告、合规检查报告
- **系统监控**: RAG性能监控、检索质量分析

### 技术特点
- **RAG专用设计**: 专门为RAG工作流程优化的用户界面
- **实时交互**: WebSocket实时通信，支持流式问答
- **可视化检索**: 检索过程可视化，结果相关性展示
- **引用溯源**: 完整的引用链路追踪和展示
- **性能监控**: 实时RAG性能指标监控
- **响应式设计**: 支持桌面端和移动端访问

## 🏗️ RAG前端架构概览

### 技术栈
```
核心框架: Vue 3.4+ (Composition API)
开发语言: TypeScript 5.3+
构建工具: Vite 5.0+
UI组件库: Element Plus 2.4+
状态管理: Pinia 2.1+
路由管理: Vue Router 4.2+
HTTP客户端: Axios 1.6+
图表库: ECharts 5.4+ + Vue-ECharts (用于可视化)
实时通信: Socket.IO Client (WebSocket)
工具库: Lodash-ES, Day.js
样式预处理: Sass
代码规范: ESLint + Prettier
```

### RAG专用架构分层
```
┌─────────────────────────────────────────┐
│              RAG交互层                   │
│     问答界面 + 检索界面 + 结果展示        │
├─────────────────────────────────────────┤
│              RAG组件层                   │
│   文档组件 + 查询组件 + 结果组件 + 可视化  │
├─────────────────────────────────────────┤
│              RAG状态层                   │
│   文档状态 + 查询状态 + 检索状态 + 结果状态 │
├─────────────────────────────────────────┤
│              RAG服务层                   │
│   文档API + 检索API + 生成API + 监控API   │
├─────────────────────────────────────────┤
│              RAG工具层                   │
│   文本处理 + 格式化 + 验证 + 缓存管理      │
└─────────────────────────────────────────┘
```

## 🚀 快速开始

### 环境要求
- Node.js 18.0+
- npm 9.0+ 或 yarn 1.22+ 或 pnpm 8.0+
- 现代浏览器 (Chrome 90+, Firefox 88+, Safari 14+)
- RAG后端服务运行中

### 安装依赖
```bash
# 进入RAG前端目录
cd rag/frontend

# 使用 npm
npm install

# 使用 yarn
yarn install

# 使用 pnpm
pnpm install
```

### 环境配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量，配置RAG后端API地址
vim .env
```

### 开发启动
```bash
# 开发模式
npm run dev

# 类型检查
npm run type-check

# 代码格式化
npm run format

# 代码检查
npm run lint
```

### 构建部署
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 📊 RAG前端项目结构

### 目录结构
```
rag/frontend/
├── public/                      # 静态资源
├── src/
│   ├── assets/                  # 资源文件
│   │   ├── images/             # 图片资源
│   │   ├── icons/              # 图标资源
│   │   └── styles/             # 全局样式
│   ├── components/              # RAG专用组件库
│   │   ├── common/             # 通用组件
│   │   │   ├── Header.vue      # 页面头部
│   │   │   ├── Sidebar.vue     # 侧边栏
│   │   │   ├── Loading.vue     # 加载组件
│   │   │   └── Pagination.vue  # 分页组件
│   │   ├── document/           # 文档管理组件
│   │   │   ├── DocumentUpload.vue    # 文档上传
│   │   │   ├── DocumentList.vue      # 文档列表
│   │   │   ├── DocumentViewer.vue    # 文档查看器
│   │   │   └── DocumentManager.vue   # 文档管理器
│   │   ├── query/              # 查询相关组件
│   │   │   ├── QueryInput.vue        # 查询输入框
│   │   │   ├── QueryHistory.vue      # 查询历史
│   │   │   ├── QueryFilters.vue      # 查询过滤器
│   │   │   └── QueryInterface.vue    # 查询界面
│   │   ├── result/             # 结果展示组件
│   │   │   ├── ResultCard.vue        # 结果卡片
│   │   │   ├── ResultList.vue        # 结果列表
│   │   │   ├── CitationView.vue      # 引用展示
│   │   │   ├── RelevanceScore.vue    # 相关性评分
│   │   │   └── AnalysisReport.vue    # 分析报告
│   │   └── visualization/      # 可视化组件
│   │       ├── KnowledgeGraph.vue    # 知识图谱
│   │       ├── RetrievalFlow.vue     # 检索流程图
│   │       └── PerformanceChart.vue  # 性能图表
│   ├── views/                   # 页面视图
│   │   ├── Dashboard.vue       # RAG仪表板
│   │   ├── DocumentManager.vue # 文档管理页
│   │   ├── QueryInterface.vue  # 智能问答页
│   │   ├── KnowledgeBase.vue   # 知识库浏览页
│   │   ├── Analytics.vue       # RAG分析统计页
│   │   └── Settings.vue        # 系统设置页
│   ├── api/                    # RAG API调用
│   │   ├── index.ts            # API配置
│   │   ├── document.ts         # 文档API
│   │   ├── query.ts            # 查询API
│   │   ├── retrieval.ts        # 检索API
│   │   ├── generation.ts       # 生成API
│   │   ├── knowledge.ts        # 知识库API
│   │   └── analytics.ts        # 分析API
│   ├── stores/                 # RAG状态管理
│   │   ├── index.ts            # Store配置
│   │   ├── document.ts         # 文档状态
│   │   ├── query.ts            # 查询状态
│   │   ├── retrieval.ts        # 检索状态
│   │   ├── generation.ts       # 生成状态
│   │   ├── user.ts             # 用户状态
│   │   └── system.ts           # 系统状态
│   ├── types/                  # TypeScript类型定义
│   │   ├── api.ts              # API类型
│   │   ├── document.ts         # 文档类型
│   │   ├── query.ts            # 查询类型
│   │   ├── retrieval.ts        # 检索类型
│   │   ├── generation.ts       # 生成类型
│   │   └── common.ts           # 通用类型
│   ├── utils/                  # 工具函数
│   │   ├── request.ts          # HTTP请求工具
│   │   ├── format.ts           # 格式化工具
│   │   ├── validation.ts       # 验证工具
│   │   ├── storage.ts          # 本地存储
│   │   ├── text-processing.ts  # 文本处理工具
│   │   └── constants.ts        # 常量定义
│   ├── composables/            # 组合式函数
│   │   ├── useDocument.ts      # 文档管理
│   │   ├── useQuery.ts         # 查询管理
│   │   ├── useRetrieval.ts     # 检索管理
│   │   └── useWebSocket.ts     # WebSocket连接
│   ├── router/                 # 路由配置
│   │   └── index.ts            # 路由定义
│   ├── App.vue                 # 根组件
│   └── main.ts                 # 入口文件
├── docs/                       # 前端文档 (本目录)
├── tests/                      # 测试文件
├── .env.example                # 环境变量示例
├── .eslintrc.js                # ESLint配置
├── .prettierrc                 # Prettier配置
├── tsconfig.json               # TypeScript配置
├── vite.config.ts              # Vite配置
└── package.json                # 项目配置
```

## 🔧 RAG专用功能特性

### 1. 智能文档管理
- **多格式支持**: PDF、DOCX、DOC、TXT等格式
- **批量上传**: 支持拖拽批量上传
- **文档预处理**: 文本提取、分块预览
- **向量化状态**: 实时显示文档向量化进度

### 2. 智能问答界面
- **自然语言输入**: 支持中英文自然语言查询
- **实时流式回答**: WebSocket流式响应展示
- **查询建议**: 基于历史查询的智能建议
- **上下文管理**: 多轮对话上下文保持

### 3. 检索结果可视化
- **相关性评分**: 可视化显示检索结果相关性
- **引用溯源**: 完整的文档引用链路展示
- **检索过程**: 可视化检索和重排序过程
- **多模态结果**: 支持文本、图表等多种结果展示

### 4. 知识库可视化
- **知识图谱**: 交互式知识关系图谱
- **分类浏览**: 按主题、类型、时间分类
- **统计分析**: 知识库覆盖度和质量分析
- **搜索过滤**: 多维度搜索和高级过滤

## 📈 性能指标

### 前端性能指标
- 首屏加载时间: < 2秒
- 查询响应时间: < 500ms
- 文档上传处理: < 10秒 (10MB文件)
- 内存使用: < 200MB
- 包大小: < 3MB (gzipped)

### RAG交互指标
- 问答延迟: < 3秒 (端到端)
- 检索结果展示: < 1秒
- 实时流式响应: < 100ms 首字节
- 并发查询支持: > 100个

## 🔗 相关链接

- [RAG前端架构设计](前端架构设计.md)
- [技术选型与规范](前端技术选型与规范.md)
- [开发指南](前端开发指南.md)
- [组件库设计](组件库设计.md)
- [API接口设计](API接口设计.md)
- [部署指南](前端部署指南.md)
- [RAG后端文档](../backend/README.md)

---

**文档版本**: v1.0.0  
**最后更新**: 2024年  
**维护状态**: 活跃维护  
**技术支持**: RAG系统前端开发团队
