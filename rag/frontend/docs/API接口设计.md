# RAG前端系统 - API接口设计

## 1. API接口概述

### 1.1 接口设计原则
- **RESTful设计**: 遵循REST架构风格
- **统一响应格式**: 标准化的响应数据结构
- **错误处理**: 完善的错误码和错误信息
- **版本控制**: 支持API版本管理
- **安全认证**: JWT Token认证机制

### 1.2 基础配置
```typescript
// src/api/config.ts
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  TIMEOUT: 30000,
  VERSION: 'v1'
}

export const API_ENDPOINTS = {
  // 文档管理
  DOCUMENTS: '/api/v1/documents',
  DOCUMENT_UPLOAD: '/api/v1/documents/upload',
  DOCUMENT_VECTORIZE: '/api/v1/documents/vectorize',
  
  // RAG查询
  RAG_QUERY: '/api/v1/rag/query',
  RAG_STREAM: '/api/v1/rag/stream',
  
  // 检索
  RETRIEVAL_SEARCH: '/api/v1/retrieval/search',
  RETRIEVAL_SIMILAR: '/api/v1/retrieval/similar',
  
  // 知识库
  KNOWLEDGE_GRAPH: '/api/v1/knowledge/graph',
  KNOWLEDGE_SEARCH: '/api/v1/knowledge/search',
  
  // 分析统计
  ANALYTICS_METRICS: '/api/v1/analytics/metrics',
  ANALYTICS_PERFORMANCE: '/api/v1/analytics/performance'
}
```

### 1.3 HTTP客户端配置
```typescript
// src/utils/request.ts
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { API_CONFIG } from '@/api/config'

// 创建axios实例
const request = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加认证token
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${userStore.token}`
      }
    }
    
    // 添加请求ID用于追踪
    config.headers = {
      ...config.headers,
      'X-Request-ID': generateRequestId()
    }
    
    // 开发环境下打印请求信息
    if (import.meta.env.DEV) {
      console.log('API请求:', config)
    }
    
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<APIResponse>) => {
    const { data } = response
    
    // 开发环境下打印响应信息
    if (import.meta.env.DEV) {
      console.log('API响应:', response)
    }
    
    // 检查业务状态码
    if (data.success) {
      return data.data
    } else {
      ElMessage.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message))
    }
  },
  (error) => {
    console.error('响应拦截器错误:', error)
    
    // 处理HTTP状态码错误
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('未授权，请重新登录')
          // 清除token并跳转到登录页
          const userStore = useUserStore()
          userStore.logout()
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || '网络错误')
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败')
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// 生成请求ID
function generateRequestId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

export { request }
```

## 2. 数据类型定义

### 2.1 通用类型
```typescript
// src/types/api.ts

// 通用API响应格式
export interface APIResponse<T = any> {
  success: boolean
  data: T
  message?: string
  code?: string
  timestamp: number
  requestId?: string
}

// 分页参数
export interface PaginationParams {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 分页响应
export interface PaginationResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 时间范围
export interface TimeRange {
  startTime: string
  endTime: string
}

// 文件上传配置
export interface UploadConfig {
  onUploadProgress?: (progressEvent: ProgressEvent) => void
  chunkSize?: number
  concurrent?: number
}
```

### 2.2 文档相关类型
```typescript
// src/types/document.ts

export interface Document {
  id: string
  name: string
  originalName: string
  size: number
  type: string
  mimeType: string
  path: string
  uploadTime: string
  status: DocumentStatus
  metadata: DocumentMetadata
  vectorizeStatus?: VectorizeStatus
}

export enum DocumentStatus {
  UPLOADING = 'uploading',
  UPLOADED = 'uploaded',
  PROCESSING = 'processing',
  PROCESSED = 'processed',
  FAILED = 'failed'
}

export interface DocumentMetadata {
  pageCount?: number
  wordCount?: number
  language?: string
  author?: string
  title?: string
  subject?: string
  keywords?: string[]
  createdTime?: string
  modifiedTime?: string
}

export interface VectorizeStatus {
  taskId: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  chunksTotal?: number
  chunksProcessed?: number
  errorMessage?: string
  startTime: string
  endTime?: string
}

export interface DocumentListParams extends PaginationParams {
  status?: DocumentStatus
  type?: string
  keyword?: string
  uploadTimeRange?: TimeRange
}

export interface DocumentUploadResponse {
  document: Document
  uploadUrl?: string
  chunkSize?: number
}
```

### 2.3 查询相关类型
```typescript
// src/types/query.ts

export interface QueryRequest {
  query: string
  filters?: QueryFilters
  options?: QueryOptions
  context?: QueryContext
}

export interface QueryFilters {
  documentIds?: string[]
  documentTypes?: string[]
  dateRange?: TimeRange
  relevanceThreshold?: number
  maxResults?: number
}

export interface QueryOptions {
  enableStreaming?: boolean
  enableCitation?: boolean
  enableFactCheck?: boolean
  temperature?: number
  maxTokens?: number
}

export interface QueryContext {
  conversationId?: string
  previousQueries?: string[]
  userPreferences?: Record<string, any>
}

export interface QueryResponse {
  id: string
  query: string
  answer: string
  sources: Source[]
  citations: Citation[]
  metadata: QueryMetadata
  conversationId?: string
}

export interface Source {
  documentId: string
  documentName: string
  chunkId: string
  content: string
  relevanceScore: number
  pageNumber?: number
  position?: TextPosition
}

export interface Citation {
  text: string
  sourceId: string
  documentName: string
  pageNumber?: number
  confidence: number
}

export interface QueryMetadata {
  retrievalTime: number
  generationTime: number
  totalTime: number
  tokensUsed: number
  model: string
  retrievalStrategy: string
  qualityScore?: number
}

export interface TextPosition {
  start: number
  end: number
  line?: number
  column?: number
}

export interface QuerySuggestion {
  text: string
  type: 'history' | 'template' | 'completion'
  count?: number
  score?: number
}

export interface QueryHistory {
  id: string
  query: string
  timestamp: string
  responseTime: number
  satisfied?: boolean
}
```

## 3. API服务实现

### 3.1 文档管理API
```typescript
// src/api/document.ts
import { request } from '@/utils/request'
import { API_ENDPOINTS } from './config'
import type {
  Document,
  DocumentListParams,
  DocumentUploadResponse,
  VectorizeStatus,
  PaginationResponse
} from '@/types'

export class DocumentAPI {
  /**
   * 获取文档列表
   */
  async getDocuments(params: DocumentListParams): Promise<PaginationResponse<Document>> {
    return request.get(API_ENDPOINTS.DOCUMENTS, { params })
  }
  
  /**
   * 获取文档详情
   */
  async getDocument(id: string): Promise<Document> {
    return request.get(`${API_ENDPOINTS.DOCUMENTS}/${id}`)
  }
  
  /**
   * 上传文档
   */
  async uploadDocument(
    file: File,
    config?: UploadConfig
  ): Promise<DocumentUploadResponse> {
    const formData = new FormData()
    formData.append('file', file)
    
    return request.post(API_ENDPOINTS.DOCUMENT_UPLOAD, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: config?.onUploadProgress
    })
  }
  
  /**
   * 分块上传文档
   */
  async uploadDocumentChunked(
    file: File,
    config?: UploadConfig
  ): Promise<DocumentUploadResponse> {
    const chunkSize = config?.chunkSize || 2 * 1024 * 1024 // 2MB
    const chunks = Math.ceil(file.size / chunkSize)
    const uploadId = generateUploadId()
    
    // 并发上传分块
    const uploadPromises = []
    for (let i = 0; i < chunks; i++) {
      const start = i * chunkSize
      const end = Math.min(start + chunkSize, file.size)
      const chunk = file.slice(start, end)
      
      uploadPromises.push(this.uploadChunk(uploadId, i, chunk, chunks))
    }
    
    await Promise.all(uploadPromises)
    
    // 合并分块
    return this.mergeChunks(uploadId, file.name)
  }
  
  /**
   * 上传文件分块
   */
  private async uploadChunk(
    uploadId: string,
    chunkIndex: number,
    chunk: Blob,
    totalChunks: number
  ): Promise<void> {
    const formData = new FormData()
    formData.append('uploadId', uploadId)
    formData.append('chunkIndex', chunkIndex.toString())
    formData.append('totalChunks', totalChunks.toString())
    formData.append('chunk', chunk)
    
    return request.post(`${API_ENDPOINTS.DOCUMENT_UPLOAD}/chunk`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
  
  /**
   * 合并文件分块
   */
  private async mergeChunks(uploadId: string, fileName: string): Promise<DocumentUploadResponse> {
    return request.post(`${API_ENDPOINTS.DOCUMENT_UPLOAD}/merge`, {
      uploadId,
      fileName
    })
  }
  
  /**
   * 删除文档
   */
  async deleteDocument(id: string): Promise<void> {
    return request.delete(`${API_ENDPOINTS.DOCUMENTS}/${id}`)
  }
  
  /**
   * 开始向量化
   */
  async startVectorization(documentId: string): Promise<VectorizeStatus> {
    return request.post(`${API_ENDPOINTS.DOCUMENT_VECTORIZE}/${documentId}/start`)
  }
  
  /**
   * 获取向量化状态
   */
  async getVectorizeStatus(taskId: string): Promise<VectorizeStatus> {
    return request.get(`${API_ENDPOINTS.DOCUMENT_VECTORIZE}/status/${taskId}`)
  }
  
  /**
   * 停止向量化
   */
  async stopVectorization(taskId: string): Promise<void> {
    return request.post(`${API_ENDPOINTS.DOCUMENT_VECTORIZE}/stop/${taskId}`)
  }
}

// 生成上传ID
function generateUploadId(): string {
  return `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

export const documentApi = new DocumentAPI()
```

### 3.2 RAG查询API
```typescript
// src/api/query.ts
import { request } from '@/utils/request'
import { API_ENDPOINTS } from './config'
import type {
  QueryRequest,
  QueryResponse,
  QuerySuggestion,
  QueryHistory,
  PaginationResponse
} from '@/types'

export class QueryAPI {
  /**
   * 执行RAG查询
   */
  async query(params: QueryRequest): Promise<QueryResponse> {
    return request.post(API_ENDPOINTS.RAG_QUERY, params)
  }
  
  /**
   * 流式RAG查询
   */
  async streamQuery(params: QueryRequest): Promise<ReadableStream<string>> {
    const response = await fetch(`${API_ENDPOINTS.RAG_STREAM}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.getToken()}`
      },
      body: JSON.stringify(params)
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    return response.body as ReadableStream<string>
  }
  
  /**
   * 获取查询建议
   */
  async getSuggestions(query: string, limit = 10): Promise<QuerySuggestion[]> {
    return request.get(`${API_ENDPOINTS.RAG_QUERY}/suggestions`, {
      params: { q: query, limit }
    })
  }
  
  /**
   * 获取查询历史
   */
  async getHistory(params: {
    page?: number
    pageSize?: number
    keyword?: string
  } = {}): Promise<PaginationResponse<QueryHistory>> {
    return request.get(`${API_ENDPOINTS.RAG_QUERY}/history`, { params })
  }
  
  /**
   * 删除查询历史
   */
  async deleteHistory(id: string): Promise<void> {
    return request.delete(`${API_ENDPOINTS.RAG_QUERY}/history/${id}`)
  }
  
  /**
   * 清空查询历史
   */
  async clearHistory(): Promise<void> {
    return request.delete(`${API_ENDPOINTS.RAG_QUERY}/history`)
  }
  
  /**
   * 查询反馈
   */
  async submitFeedback(queryId: string, feedback: {
    rating: number
    comment?: string
    helpful: boolean
  }): Promise<void> {
    return request.post(`${API_ENDPOINTS.RAG_QUERY}/${queryId}/feedback`, feedback)
  }
  
  private getToken(): string {
    // 从store或localStorage获取token
    return localStorage.getItem('access_token') || ''
  }
}

export const queryApi = new QueryAPI()
```

### 3.3 WebSocket服务
```typescript
// src/api/websocket.ts
import { io, Socket } from 'socket.io-client'
import { API_CONFIG } from './config'
import type { QueryRequest, QueryResponse } from '@/types'

export class WebSocketService {
  private socket: Socket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  
  /**
   * 连接WebSocket
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.socket = io(API_CONFIG.BASE_URL, {
        transports: ['websocket'],
        auth: {
          token: this.getToken()
        }
      })
      
      this.socket.on('connect', () => {
        console.log('WebSocket连接成功')
        this.reconnectAttempts = 0
        resolve()
      })
      
      this.socket.on('disconnect', (reason) => {
        console.log('WebSocket断开连接:', reason)
        this.handleReconnect()
      })
      
      this.socket.on('connect_error', (error) => {
        console.error('WebSocket连接错误:', error)
        reject(error)
      })
    })
  }
  
  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
  }
  
  /**
   * 发送RAG查询
   */
  sendQuery(request: QueryRequest): void {
    if (this.socket?.connected) {
      this.socket.emit('rag_query', request)
    } else {
      throw new Error('WebSocket未连接')
    }
  }
  
  /**
   * 监听答案流
   */
  onAnswerStream(callback: (chunk: string) => void): void {
    this.socket?.on('rag_answer_chunk', callback)
  }
  
  /**
   * 监听查询完成
   */
  onQueryComplete(callback: (response: QueryResponse) => void): void {
    this.socket?.on('rag_answer_complete', callback)
  }
  
  /**
   * 监听错误
   */
  onError(callback: (error: string) => void): void {
    this.socket?.on('rag_error', callback)
  }
  
  /**
   * 移除监听器
   */
  off(event: string, callback?: Function): void {
    if (callback) {
      this.socket?.off(event, callback)
    } else {
      this.socket?.off(event)
    }
  }
  
  /**
   * 获取连接状态
   */
  get isConnected(): boolean {
    return this.socket?.connected || false
  }
  
  /**
   * 处理重连
   */
  private handleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect().catch(console.error)
      }, this.reconnectDelay * this.reconnectAttempts)
    } else {
      console.error('WebSocket重连失败，已达到最大重试次数')
    }
  }
  
  private getToken(): string {
    return localStorage.getItem('access_token') || ''
  }
}

export const websocketService = new WebSocketService()
```

## 4. 错误处理

### 4.1 错误类型定义
```typescript
// src/types/error.ts

export interface APIError {
  code: string
  message: string
  details?: Record<string, any>
  timestamp: number
  requestId?: string
}

export enum ErrorCode {
  // 通用错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  
  // 认证错误
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  
  // 业务错误
  DOCUMENT_NOT_FOUND = 'DOCUMENT_NOT_FOUND',
  DOCUMENT_TOO_LARGE = 'DOCUMENT_TOO_LARGE',
  UNSUPPORTED_FORMAT = 'UNSUPPORTED_FORMAT',
  VECTORIZATION_FAILED = 'VECTORIZATION_FAILED',
  
  // RAG错误
  QUERY_TOO_LONG = 'QUERY_TOO_LONG',
  NO_RELEVANT_DOCUMENTS = 'NO_RELEVANT_DOCUMENTS',
  GENERATION_FAILED = 'GENERATION_FAILED'
}
```

### 4.2 错误处理工具
```typescript
// src/utils/error.ts
import { ElMessage, ElNotification } from 'element-plus'
import type { APIError, ErrorCode } from '@/types/error'

export class ErrorHandler {
  private static errorMessages: Record<string, string> = {
    [ErrorCode.UNKNOWN_ERROR]: '未知错误',
    [ErrorCode.NETWORK_ERROR]: '网络连接失败',
    [ErrorCode.TIMEOUT_ERROR]: '请求超时',
    [ErrorCode.UNAUTHORIZED]: '未授权，请重新登录',
    [ErrorCode.FORBIDDEN]: '权限不足',
    [ErrorCode.TOKEN_EXPIRED]: '登录已过期，请重新登录',
    [ErrorCode.DOCUMENT_NOT_FOUND]: '文档不存在',
    [ErrorCode.DOCUMENT_TOO_LARGE]: '文档大小超出限制',
    [ErrorCode.UNSUPPORTED_FORMAT]: '不支持的文件格式',
    [ErrorCode.VECTORIZATION_FAILED]: '文档向量化失败',
    [ErrorCode.QUERY_TOO_LONG]: '查询内容过长',
    [ErrorCode.NO_RELEVANT_DOCUMENTS]: '未找到相关文档',
    [ErrorCode.GENERATION_FAILED]: '答案生成失败'
  }
  
  static handle(error: APIError | Error, showNotification = true): void {
    let message: string
    let code: string
    
    if ('code' in error) {
      // API错误
      code = error.code
      message = this.errorMessages[code] || error.message
    } else {
      // 普通错误
      code = ErrorCode.UNKNOWN_ERROR
      message = error.message || '未知错误'
    }
    
    // 记录错误日志
    console.error('错误处理:', { code, message, error })
    
    // 显示错误提示
    if (showNotification) {
      if (this.isCriticalError(code)) {
        ElNotification.error({
          title: '系统错误',
          message,
          duration: 0 // 不自动关闭
        })
      } else {
        ElMessage.error(message)
      }
    }
    
    // 特殊错误处理
    this.handleSpecialErrors(code)
  }
  
  private static isCriticalError(code: string): boolean {
    return [
      ErrorCode.UNAUTHORIZED,
      ErrorCode.TOKEN_EXPIRED,
      ErrorCode.NETWORK_ERROR
    ].includes(code as ErrorCode)
  }
  
  private static handleSpecialErrors(code: string): void {
    switch (code) {
      case ErrorCode.UNAUTHORIZED:
      case ErrorCode.TOKEN_EXPIRED:
        // 清除token并跳转到登录页
        localStorage.removeItem('access_token')
        window.location.href = '/login'
        break
    }
  }
}
```

---

**文档版本**: v1.0.0  
**最后更新**: 2024年  
**维护状态**: 活跃维护
