# RAG前端系统 - 部署指南

## 1. 部署概述

### 1.1 部署架构
RAG前端系统支持多种部署方式，包括开发环境、测试环境和生产环境的部署配置。

### 1.2 部署环境要求
- **Node.js**: 18.0+
- **npm/yarn/pnpm**: 最新版本
- **Web服务器**: Nginx 1.20+ 或 Apache 2.4+
- **容器环境**: Docker 20.0+ (可选)
- **CDN**: 支持静态资源CDN加速 (可选)

## 2. 构建配置

### 2.1 环境变量配置

#### 开发环境 (.env.development)
```bash
# 应用配置
VITE_APP_TITLE=RAG智能分析系统 - 开发环境
VITE_APP_VERSION=1.0.0-dev
VITE_APP_ENV=development

# API配置
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000/ws
VITE_API_TIMEOUT=30000

# 功能开关
VITE_ENABLE_MOCK=true
VITE_ENABLE_DEBUG=true
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_HOT_RELOAD=true

# 上传配置
VITE_UPLOAD_MAX_SIZE=100
VITE_UPLOAD_CHUNK_SIZE=2
VITE_UPLOAD_CONCURRENT=3

# RAG配置
VITE_RAG_MAX_CONTEXT_LENGTH=4000
VITE_RAG_TOP_K=10
VITE_RAG_SIMILARITY_THRESHOLD=0.7
```

#### 生产环境 (.env.production)
```bash
# 应用配置
VITE_APP_TITLE=RAG智能分析系统
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=production

# API配置
VITE_API_BASE_URL=https://api.rag-system.com
VITE_WS_URL=wss://api.rag-system.com/ws
VITE_API_TIMEOUT=30000

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEBUG=false
VITE_ENABLE_DEVTOOLS=false
VITE_ENABLE_HOT_RELOAD=false

# 性能配置
VITE_ENABLE_GZIP=true
VITE_ENABLE_BUNDLE_ANALYZER=false
VITE_CHUNK_SIZE_WARNING_LIMIT=1000

# CDN配置
VITE_CDN_URL=https://cdn.rag-system.com
VITE_STATIC_URL=https://static.rag-system.com

# 监控配置
VITE_ENABLE_SENTRY=true
VITE_SENTRY_DSN=https://your-sentry-dsn
```

### 2.2 Vite构建配置

```typescript
// vite.config.ts
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { visualizer } from 'rollup-plugin-visualizer'
import { createHtmlPlugin } from 'vite-plugin-html'
import { viteCommonjs } from '@originjs/vite-plugin-commonjs'

export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  const isProduction = mode === 'production'
  
  return {
    plugins: [
      vue(),
      viteCommonjs(),
      
      // HTML模板插件
      createHtmlPlugin({
        inject: {
          data: {
            title: env.VITE_APP_TITLE,
            version: env.VITE_APP_VERSION
          }
        }
      }),
      
      // 构建分析插件（仅生产环境）
      isProduction && env.VITE_ENABLE_BUNDLE_ANALYZER === 'true' && visualizer({
        filename: 'dist/stats.html',
        open: true,
        gzipSize: true
      })
    ].filter(Boolean),
    
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@components': resolve(__dirname, 'src/components'),
        '@views': resolve(__dirname, 'src/views'),
        '@api': resolve(__dirname, 'src/api'),
        '@utils': resolve(__dirname, 'src/utils'),
        '@types': resolve(__dirname, 'src/types'),
        '@assets': resolve(__dirname, 'src/assets')
      }
    },
    
    server: {
      host: '0.0.0.0',
      port: 3000,
      open: true,
      proxy: {
        '/api': {
          target: env.VITE_API_BASE_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '/api/v1')
        },
        '/ws': {
          target: env.VITE_WS_URL,
          ws: true,
          changeOrigin: true
        }
      }
    },
    
    build: {
      target: 'es2015',
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: !isProduction,
      minify: isProduction ? 'terser' : false,
      
      // 代码分割配置
      rollupOptions: {
        output: {
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
          
          // 手动分包
          manualChunks: {
            // Vue相关
            vue: ['vue', 'vue-router', 'pinia'],
            
            // UI组件库
            'element-plus': ['element-plus', '@element-plus/icons-vue'],
            
            // 图表库
            echarts: ['echarts', 'vue-echarts'],
            
            // 工具库
            utils: ['lodash-es', 'dayjs', 'axios'],
            
            // 业务模块
            'rag-core': [
              './src/api/query.ts',
              './src/api/document.ts',
              './src/composables/useRAGQuery.ts'
            ]
          }
        }
      },
      
      // Terser压缩配置
      terserOptions: {
        compress: {
          drop_console: isProduction,
          drop_debugger: isProduction,
          pure_funcs: isProduction ? ['console.log'] : []
        }
      },
      
      // 构建警告配置
      chunkSizeWarningLimit: parseInt(env.VITE_CHUNK_SIZE_WARNING_LIMIT) || 500
    },
    
    // CSS配置
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `
            @import "@/assets/styles/variables.scss";
            @import "@/assets/styles/mixins.scss";
          `
        }
      }
    },
    
    // 优化配置
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'element-plus',
        'axios',
        'lodash-es',
        'dayjs'
      ]
    }
  }
})
```

### 2.3 构建脚本

```json
{
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build",
    "build:dev": "vue-tsc && vite build --mode development",
    "build:test": "vue-tsc && vite build --mode test",
    "build:prod": "vue-tsc && vite build --mode production",
    "preview": "vite preview",
    "analyze": "VITE_ENABLE_BUNDLE_ANALYZER=true npm run build",
    "type-check": "vue-tsc --noEmit",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix",
    "format": "prettier --write src/"
  }
}
```

## 3. Docker部署

### 3.1 Dockerfile

```dockerfile
# 多阶段构建
FROM node:18-alpine as builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./
COPY pnpm-lock.yaml ./

# 安装pnpm
RUN npm install -g pnpm

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建应用
RUN pnpm run build

# 生产阶段
FROM nginx:alpine

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf
COPY default.conf /etc/nginx/conf.d/default.conf

# 创建日志目录
RUN mkdir -p /var/log/nginx

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
```

### 3.2 Nginx配置

```nginx
# nginx.conf
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    
    # 基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 包含站点配置
    include /etc/nginx/conf.d/*.conf;
}
```

```nginx
# default.conf
server {
    listen 80;
    server_name _;
    root /usr/share/nginx/html;
    index index.html;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # HTML文件不缓存
    location ~* \.html$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://rag-backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # WebSocket代理
    location /ws/ {
        proxy_pass http://rag-backend:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket超时配置
        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
    }
    
    # Vue Router History模式支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 错误页面
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
```

### 3.3 Docker Compose配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  rag-frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rag-frontend
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./default.conf:/etc/nginx/conf.d/default.conf:ro
      - nginx-logs:/var/log/nginx
    depends_on:
      - rag-backend
    networks:
      - rag-network
    restart: unless-stopped
    
  # 如果需要HTTPS
  rag-frontend-ssl:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rag-frontend-ssl
    ports:
      - "443:443"
    volumes:
      - ./ssl.conf:/etc/nginx/conf.d/ssl.conf:ro
      - ./certs:/etc/nginx/certs:ro
      - nginx-logs:/var/log/nginx
    networks:
      - rag-network
    restart: unless-stopped

volumes:
  nginx-logs:

networks:
  rag-network:
    external: true
```

## 4. 生产环境部署

### 4.1 服务器配置

#### 系统要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **内存**: 最少2GB，推荐4GB+
- **存储**: 最少10GB可用空间
- **网络**: 稳定的网络连接

#### 安装依赖
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y nginx nodejs npm docker.io docker-compose

# CentOS/RHEL
sudo yum update -y
sudo yum install -y nginx nodejs npm docker docker-compose

# 启动服务
sudo systemctl enable nginx docker
sudo systemctl start nginx docker
```

### 4.2 部署脚本

```bash
#!/bin/bash
# deploy.sh - RAG前端部署脚本

set -e

# 配置变量
PROJECT_NAME="rag-frontend"
DEPLOY_DIR="/opt/rag-frontend"
BACKUP_DIR="/opt/backups/rag-frontend"
NGINX_CONF="/etc/nginx/sites-available/rag-frontend"
DOMAIN="rag-system.com"

echo "开始部署RAG前端系统..."

# 创建部署目录
sudo mkdir -p $DEPLOY_DIR $BACKUP_DIR

# 备份当前版本
if [ -d "$DEPLOY_DIR/current" ]; then
    echo "备份当前版本..."
    sudo cp -r $DEPLOY_DIR/current $BACKUP_DIR/$(date +%Y%m%d_%H%M%S)
fi

# 构建应用
echo "构建应用..."
npm ci
npm run build:prod

# 部署新版本
echo "部署新版本..."
sudo rm -rf $DEPLOY_DIR/current
sudo mkdir -p $DEPLOY_DIR/current
sudo cp -r dist/* $DEPLOY_DIR/current/

# 配置Nginx
echo "配置Nginx..."
sudo tee $NGINX_CONF > /dev/null <<EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    root $DEPLOY_DIR/current;
    index index.html;
    
    # 重定向到HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;
    root $DEPLOY_DIR/current;
    index index.html;
    
    # SSL配置
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理
    location /api/ {
        proxy_pass https://api.$DOMAIN;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # Vue Router支持
    location / {
        try_files \$uri \$uri/ /index.html;
    }
}
EOF

# 启用站点
sudo ln -sf $NGINX_CONF /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

echo "部署完成！"
echo "访问地址: https://$DOMAIN"
```

### 4.3 监控和日志

#### 日志配置
```bash
# 创建日志目录
sudo mkdir -p /var/log/rag-frontend

# 配置日志轮转
sudo tee /etc/logrotate.d/rag-frontend > /dev/null <<EOF
/var/log/nginx/rag-frontend*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 nginx nginx
    postrotate
        if [ -f /var/run/nginx.pid ]; then
            kill -USR1 \$(cat /var/run/nginx.pid)
        fi
    endscript
}
EOF
```

#### 性能监控
```bash
# 安装监控工具
sudo apt install -y htop iotop nethogs

# 创建监控脚本
sudo tee /opt/scripts/monitor.sh > /dev/null <<'EOF'
#!/bin/bash
# 系统监控脚本

LOG_FILE="/var/log/rag-frontend/monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# CPU使用率
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')

# 内存使用率
MEM_USAGE=$(free | grep Mem | awk '{printf("%.2f"), $3/$2 * 100.0}')

# 磁盘使用率
DISK_USAGE=$(df -h / | awk 'NR==2{printf "%s", $5}')

# Nginx状态
NGINX_STATUS=$(systemctl is-active nginx)

echo "[$DATE] CPU: ${CPU_USAGE}%, MEM: ${MEM_USAGE}%, DISK: ${DISK_USAGE}, NGINX: ${NGINX_STATUS}" >> $LOG_FILE
EOF

chmod +x /opt/scripts/monitor.sh

# 添加到crontab
(crontab -l 2>/dev/null; echo "*/5 * * * * /opt/scripts/monitor.sh") | crontab -
```

## 5. 故障排除

### 5.1 常见问题

#### 构建失败
```bash
# 清理缓存
rm -rf node_modules package-lock.json
npm cache clean --force
npm install

# 检查Node.js版本
node --version  # 应该 >= 18.0.0
```

#### 部署后页面空白
```bash
# 检查构建产物
ls -la dist/

# 检查Nginx配置
sudo nginx -t
sudo systemctl status nginx

# 查看错误日志
sudo tail -f /var/log/nginx/error.log
```

#### API请求失败
```bash
# 检查代理配置
curl -I http://localhost/api/health

# 检查后端服务
curl -I http://rag-backend:8000/api/v1/health

# 查看网络连接
netstat -tlnp | grep :80
```

### 5.2 性能优化

#### 启用HTTP/2
```nginx
server {
    listen 443 ssl http2;
    # ... 其他配置
}
```

#### 启用Brotli压缩
```bash
# 安装Brotli模块
sudo apt install nginx-module-brotli

# 配置Brotli
load_module modules/ngx_http_brotli_filter_module.so;
load_module modules/ngx_http_brotli_static_module.so;

http {
    brotli on;
    brotli_comp_level 6;
    brotli_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}
```

---

**文档版本**: v1.0.0  
**最后更新**: 2024年  
**维护状态**: 活跃维护
