# RAG前端界面

RAG系统的前端界面，提供法律文档管理、智能问答、结果可视化等功能。

## 📁 目录结构

```
frontend/
├── src/                     # 源代码
│   ├── components/          # Vue组件
│   │   ├── common/         # 通用组件
│   │   │   ├── Header.vue  # 页面头部
│   │   │   ├── Sidebar.vue # 侧边栏
│   │   │   ├── Loading.vue # 加载组件
│   │   │   └── Pagination.vue # 分页组件
│   │   ├── document/       # 文档相关组件
│   │   │   ├── DocumentUpload.vue # 文档上传
│   │   │   ├── DocumentList.vue   # 文档列表
│   │   │   ├── DocumentViewer.vue # 文档查看器
│   │   │   └── DocumentManager.vue # 文档管理
│   │   ├── query/          # 查询相关组件
│   │   │   ├── QueryInput.vue     # 查询输入框
│   │   │   ├── QueryHistory.vue   # 查询历史
│   │   │   ├── QueryFilters.vue   # 查询过滤器
│   │   │   └── QueryInterface.vue # 查询界面
│   │   └── result/         # 结果展示组件
│   │       ├── ResultCard.vue     # 结果卡片
│   │       ├── ResultList.vue     # 结果列表
│   │       ├── CitationView.vue   # 引用展示
│   │       └── AnalysisReport.vue # 分析报告
│   ├── views/              # 页面视图
│   │   ├── Dashboard.vue   # 仪表板
│   │   ├── DocumentManager.vue # 文档管理页
│   │   ├── QueryInterface.vue  # 查询界面页
│   │   ├── KnowledgeBase.vue   # 知识库浏览页
│   │   ├── Analytics.vue       # 分析统计页
│   │   └── Settings.vue        # 系统设置页
│   ├── api/                # API调用
│   │   ├── index.ts        # API配置
│   │   ├── document.ts     # 文档API
│   │   ├── query.ts        # 查询API
│   │   ├── knowledge.ts    # 知识库API
│   │   └── analytics.ts    # 分析API
│   ├── utils/              # 工具函数
│   │   ├── request.ts      # HTTP请求工具
│   │   ├── format.ts       # 格式化工具
│   │   ├── validation.ts   # 表单验证
│   │   ├── storage.ts      # 本地存储
│   │   └── constants.ts    # 常量定义
│   ├── stores/             # 状态管理
│   │   ├── index.ts        # Store配置
│   │   ├── document.ts     # 文档状态
│   │   ├── query.ts        # 查询状态
│   │   ├── user.ts         # 用户状态
│   │   └── system.ts       # 系统状态
│   ├── types/              # TypeScript类型
│   │   ├── api.ts          # API类型定义
│   │   ├── document.ts     # 文档类型
│   │   ├── query.ts        # 查询类型
│   │   ├── user.ts         # 用户类型
│   │   └── common.ts       # 通用类型
│   ├── assets/             # 静态资源
│   │   ├── css/            # 样式文件
│   │   │   ├── main.css    # 主样式
│   │   │   ├── variables.css # CSS变量
│   │   │   └── components.css # 组件样式
│   │   ├── images/         # 图片资源
│   │   └── icons/          # 图标资源
│   ├── router/             # 路由配置
│   │   └── index.ts        # 路由定义
│   ├── App.vue             # 根组件
│   └── main.ts             # 应用入口
├── public/                 # 公共文件
│   ├── index.html          # HTML模板
│   └── favicon.ico         # 网站图标
├── package.json            # 项目配置
├── vite.config.ts          # Vite配置
├── tsconfig.json           # TypeScript配置
├── .env.example            # 环境变量示例
└── README.md               # 本文档
```

## 🚀 快速开始

### 环境要求

- Node.js 18+
- npm 8+ 或 yarn 1.22+

### 安装依赖

```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

### 启动开发服务器

```bash
# 使用npm
npm run dev

# 或使用yarn
yarn dev
```

### 构建生产版本

```bash
# 使用npm
npm run build

# 或使用yarn
yarn build
```

## 🔧 配置说明

### 环境变量 (.env)

```bash
# 应用配置
VITE_APP_TITLE=RAG智能分析系统
VITE_APP_VERSION=1.0.0

# API配置
VITE_API_BASE_URL=http://localhost:8000
VITE_API_TIMEOUT=30000

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEBUG=true

# 上传配置
VITE_UPLOAD_MAX_SIZE=100
VITE_UPLOAD_ALLOWED_TYPES=pdf,docx,doc,txt

# 界面配置
VITE_DEFAULT_LANGUAGE=zh-CN
VITE_DEFAULT_THEME=light
```

### Vite配置 (vite.config.ts)

```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '/api/v1')
      }
    }
  }
})
```

## 🎨 主要功能

### 1. 文档管理
- **文档上传**: 支持拖拽上传、批量上传
- **文档预览**: 在线预览PDF、Word等格式
- **文档分类**: 按类型、日期、标签分类管理
- **文档搜索**: 全文搜索、元数据搜索

### 2. 智能问答
- **自然语言查询**: 支持中英文自然语言输入
- **查询建议**: 智能查询建议和自动补全
- **历史记录**: 查询历史管理和快速重用
- **结果展示**: 结构化结果展示和高亮显示

### 3. 知识库浏览
- **知识图谱**: 可视化知识关系图
- **分类浏览**: 按主题、类型浏览知识
- **搜索过滤**: 多维度搜索和过滤
- **统计分析**: 知识库统计和分析

### 4. 分析报告
- **风险评估**: 合同风险分析报告
- **合规检查**: 法规合规性检查报告
- **对比分析**: 多文档对比分析
- **导出功能**: 支持PDF、Word格式导出

## 🧩 核心组件

### DocumentUpload 文档上传组件

```vue
<template>
  <DocumentUpload
    :accept="['pdf', 'docx', 'doc']"
    :max-size="100"
    :multiple="true"
    @upload="handleUpload"
    @error="handleError"
  />
</template>
```

### QueryInterface 查询界面组件

```vue
<template>
  <QueryInterface
    v-model:query="queryText"
    :loading="isLoading"
    :suggestions="suggestions"
    @search="handleSearch"
    @clear="handleClear"
  />
</template>
```

### ResultCard 结果卡片组件

```vue
<template>
  <ResultCard
    :result="result"
    :show-citation="true"
    :highlight-keywords="keywords"
    @view-detail="handleViewDetail"
  />
</template>
```

## 📱 响应式设计

- **桌面端**: 1200px+ 完整功能界面
- **平板端**: 768px-1199px 适配触摸操作
- **移动端**: <768px 简化界面，核心功能

## 🎨 主题定制

### CSS变量定义

```css
:root {
  /* 主色调 */
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  
  /* 中性色 */
  --text-color: #262626;
  --text-color-secondary: #8c8c8c;
  --border-color: #d9d9d9;
  --background-color: #f5f5f5;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}
```

## 🧪 测试

```bash
# 运行单元测试
npm run test

# 运行E2E测试
npm run test:e2e

# 生成测试覆盖率报告
npm run test:coverage
```

## 📦 构建部署

### 开发环境

```bash
npm run dev
```

### 生产构建

```bash
npm run build
```

### 预览构建结果

```bash
npm run preview
```

### Docker部署

```dockerfile
FROM node:18-alpine as builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 🔗 相关文档

- [Vue 3 官方文档](https://vuejs.org/)
- [Element Plus 组件库](https://element-plus.org/)
- [Vite 构建工具](https://vitejs.dev/)
- [TypeScript 文档](https://www.typescriptlang.org/)
- [RAG后端API文档](../backend/README.md)
