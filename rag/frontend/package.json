{"name": "rag-frontend", "version": "1.0.0", "description": "RAG系统前端界面 - 法律文档智能分析", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit", "test": "vitest", "test:coverage": "vitest --coverage"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "marked": "^11.1.1", "highlight.js": "^11.9.0", "file-saver": "^2.0.5", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "nprogress": "^0.2.0"}, "devDependencies": {"@types/node": "^20.10.5", "@types/file-saver": "^2.0.7", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "typescript": "~5.3.3", "vite": "^5.0.10", "vue-tsc": "^1.8.25", "vitest": "^1.1.0", "@vitest/coverage-v8": "^1.1.0", "jsdom": "^23.0.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}