# RAG系统环境变量配置

# ===========================================
# 基础配置
# ===========================================
RAG_ENV=development
RAG_DEBUG=true
RAG_LOG_LEVEL=INFO
RAG_LOG_FILE=./logs/rag.log

# API服务配置
RAG_HOST=0.0.0.0
RAG_PORT=8001
RAG_WORKERS=4

# ===========================================
# AI模型配置
# ===========================================
# 千问API配置（推荐）
DASHSCOPE_API_KEY=your-dashscope-api-key-here
QWEN_MODEL_MAX=qwen-max
QWEN_MODEL_PLUS=qwen-plus
QWEN_MODEL_FLASH=qwen-flash

# OpenAI配置（备选）
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_BASE_URL=https://api.openai.com/v1

# ===========================================
# 嵌入模型配置
# ===========================================
RAG_EMBEDDING_MODEL=text-embedding-v3
RAG_EMBEDDING_DIMS=1536
RAG_EMBEDDING_BATCH_SIZE=100
RAG_EMBEDDING_MAX_RETRIES=3

# 本地嵌入模型配置
LOCAL_EMBEDDING_MODEL=BAAI/bge-large-zh-v1.5
LOCAL_EMBEDDING_DEVICE=auto

# ===========================================
# 向量数据库配置
# ===========================================
# 向量存储提供商: chroma, weaviate, pinecone, milvus
RAG_VECTOR_STORE=chroma
RAG_COLLECTION_NAME=contract_knowledge
RAG_DISTANCE_METRIC=cosine

# ChromaDB配置
CHROMA_HOST=localhost
CHROMA_PORT=8000
CHROMA_PERSIST_DIR=./data/chroma_db

# Weaviate配置
WEAVIATE_URL=http://localhost:8080
WEAVIATE_API_KEY=your-weaviate-api-key

# Pinecone配置
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=your-pinecone-environment
PINECONE_INDEX_NAME=contract-knowledge

# ===========================================
# 检索配置
# ===========================================
# 检索策略: vector_only, keyword_only, hybrid, semantic
RAG_RETRIEVAL_STRATEGY=hybrid
RAG_TOP_K=10
RAG_SIMILARITY_THRESHOLD=0.7

# 向量检索配置
RAG_VECTOR_TOP_K=20
RAG_VECTOR_THRESHOLD=0.6

# 关键词检索配置
RAG_KEYWORD_TOP_K=10
RAG_KEYWORD_ANALYZER=standard

# 混合检索权重
RAG_HYBRID_ALPHA=0.7
RAG_HYBRID_BETA=0.3

# 重排序配置
RAG_ENABLE_RERANK=true
RAG_RERANK_MODEL=cross-encoder
RAG_RERANK_TOP_K=5

# ===========================================
# 生成配置
# ===========================================
RAG_MAX_CONTEXT_LENGTH=4000
RAG_CONTEXT_OVERLAP=200
RAG_MAX_TOKENS=2000
RAG_RESERVED_TOKENS=500

# 质量控制
RAG_ENABLE_HALLUCINATION_DETECTION=true
RAG_ENABLE_FACT_CHECKING=true
RAG_ENABLE_CITATION=true

# 提示模板
RAG_SYSTEM_PROMPT_TEMPLATE=default
RAG_USER_PROMPT_TEMPLATE=default

# ===========================================
# 缓存配置
# ===========================================
RAG_ENABLE_CACHE=true
RAG_CACHE_TYPE=redis

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=1
REDIS_PASSWORD=your-redis-password

# 缓存TTL（秒）
RAG_QUERY_CACHE_TTL=3600
RAG_EMBEDDING_CACHE_TTL=86400
RAG_RESULT_CACHE_TTL=1800

# ===========================================
# 知识库配置
# ===========================================
RAG_KNOWLEDGE_BASE_PATH=./data/knowledge_base
RAG_SUPPORTED_FORMATS=pdf,docx,doc,txt,md

# 文本切分配置
RAG_CHUNK_SIZE=1000
RAG_CHUNK_OVERLAP=200
RAG_CHUNK_STRATEGY=recursive

# 文档摄取配置
RAG_MAX_FILE_SIZE=50MB
RAG_BATCH_SIZE=10
RAG_PARALLEL_WORKERS=4

# ===========================================
# 监控配置
# ===========================================
RAG_ENABLE_MONITORING=true
RAG_METRICS_PORT=8080

# 性能监控
RAG_TRACK_LATENCY=true
RAG_TRACK_THROUGHPUT=true
RAG_TRACK_ACCURACY=true

# 告警阈值
RAG_ALERT_LATENCY_THRESHOLD=1.0
RAG_ALERT_ERROR_RATE_THRESHOLD=0.05

# Prometheus配置
PROMETHEUS_GATEWAY=localhost:9091
PROMETHEUS_JOB_NAME=rag-system

# ===========================================
# 数据库配置
# ===========================================
# PostgreSQL配置（用于元数据存储）
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=rag_metadata
POSTGRES_USER=rag_user
POSTGRES_PASSWORD=your-postgres-password

# 数据库连接池
POSTGRES_POOL_SIZE=10
POSTGRES_MAX_OVERFLOW=20

# ===========================================
# 安全配置
# ===========================================
RAG_SECRET_KEY=your-secret-key-here
RAG_ACCESS_TOKEN_EXPIRE_MINUTES=30
RAG_ALGORITHM=HS256

# CORS配置
RAG_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
RAG_CORS_METHODS=["GET", "POST", "PUT", "DELETE"]

# ===========================================
# 开发配置
# ===========================================
# 开发模式配置
RAG_AUTO_RELOAD=true
RAG_DEBUG_SQL=false

# 测试配置
RAG_TEST_DB=rag_test
RAG_TEST_COLLECTION=test_knowledge

# ===========================================
# 生产配置
# ===========================================
# 生产环境特定配置
RAG_PRODUCTION_MODE=false
RAG_SSL_KEYFILE=./certs/key.pem
RAG_SSL_CERTFILE=./certs/cert.pem

# 负载均衡配置
RAG_LOAD_BALANCER=nginx
RAG_UPSTREAM_SERVERS=["rag1:8001", "rag2:8001", "rag3:8001"]

# 备份配置
RAG_BACKUP_ENABLED=true
RAG_BACKUP_SCHEDULE="0 2 * * *"
RAG_BACKUP_RETENTION_DAYS=30

# ===========================================
# 特性开关
# ===========================================
# 实验性功能开关
RAG_ENABLE_KNOWLEDGE_GRAPH=false
RAG_ENABLE_MULTIMODAL=false
RAG_ENABLE_REAL_TIME_UPDATE=false
RAG_ENABLE_AUTO_OPTIMIZATION=false

# A/B测试配置
RAG_AB_TEST_ENABLED=false
RAG_AB_TEST_RATIO=0.1
