#!/usr/bin/env python3
"""
RAG系统安装配置
"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取README文件
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding='utf-8') if (this_directory / "README.md").exists() else ""

# 读取requirements
def read_requirements(filename):
    """读取requirements文件"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    except FileNotFoundError:
        return []

# 基础依赖
install_requires = [
    "fastapi>=0.100.0",
    "uvicorn[standard]>=0.23.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "httpx>=0.24.0",
    "asyncpg>=0.28.0",
    "redis>=4.5.0",
    "numpy>=1.24.0",
    "jieba>=0.42.1",
]

# 可选依赖
extras_require = {
    'dev': [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.0.0",
        "pytest-mock>=3.10.0",
        "pytest-html>=3.1.0",
        "pytest-xdist>=3.0.0",
        "black>=23.0.0",
        "flake8>=6.0.0",
        "mypy>=1.0.0",
    ],
    'ai': [
        "openai>=1.0.0",
        "chromadb>=0.4.0",
    ],
    'docs': [
        "PyPDF2>=3.0.0",
        "python-docx>=0.8.11",
        "openpyxl>=3.1.0",
    ],
    'all': [
        "openai>=1.0.0",
        "chromadb>=0.4.0",
        "PyPDF2>=3.0.0",
        "python-docx>=0.8.11",
        "openpyxl>=3.1.0",
    ]
}

setup(
    name="rag-system",
    version="1.0.0",
    description="检索增强生成(RAG)系统",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="RAG Team",
    author_email="<EMAIL>",
    url="https://github.com/your-org/rag-system",
    
    # 包配置
    packages=find_packages(),
    python_requires=">=3.8",
    
    # 依赖配置
    install_requires=install_requires,
    extras_require=extras_require,
    
    # 包数据
    include_package_data=True,
    package_data={
        "": ["*.txt", "*.md", "*.yml", "*.yaml", "*.json"],
    },
    
    # 入口点
    entry_points={
        "console_scripts": [
            "rag-server=main:main",
            "rag-test=run_tests:main",
        ],
    },
    
    # 分类信息
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    
    # 关键词
    keywords="rag, retrieval, generation, ai, nlp, chatbot",
    
    # 项目URL
    project_urls={
        "Bug Reports": "https://github.com/your-org/rag-system/issues",
        "Source": "https://github.com/your-org/rag-system",
        "Documentation": "https://rag-system.readthedocs.io/",
    },
)
