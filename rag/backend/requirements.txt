# RAG系统核心依赖

# 基础框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# AI和机器学习
langchain==0.1.0
langchain-community==0.0.10
llama-index==0.9.30
transformers==4.36.0
torch==2.1.0
sentence-transformers==2.2.2

# 向量数据库
chromadb==0.4.18
weaviate-client==3.25.3
pinecone-client==2.2.4

# 文档处理
pypdf==3.17.1
python-docx==1.1.0
unstructured==0.11.6
python-multipart==0.0.6

# 嵌入模型
dashscope==1.14.1
openai==1.6.1

# 数据处理
numpy==1.24.3
pandas==2.0.3
scikit-learn==1.3.0

# 缓存和存储
redis==5.0.1
sqlalchemy==2.0.23
alembic==1.13.1

# 文本处理
jieba==0.42.1
nltk==3.8.1
spacy==3.7.2

# 网络和API
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# 监控和日志
prometheus-client==0.19.0
structlog==23.2.0
loguru==0.7.2

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0

# 开发工具
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# 配置管理
python-dotenv==1.0.0
pyyaml==6.0.1
toml==0.10.2

# 异步支持
asyncio==3.4.3
aiofiles==23.2.1

# 数学和科学计算
scipy==1.11.4
matplotlib==3.8.2

# 其他工具
tqdm==4.66.1
click==8.1.7
rich==13.7.0
