"""
监控和指标收集模块
"""

import time
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, deque


@dataclass
class MetricPoint:
    """指标数据点"""
    name: str
    value: float
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, max_points: int = 10000):
        self.max_points = max_points
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_points))
        self.counters: Dict[str, float] = defaultdict(float)
        self.gauges: Dict[str, float] = {}
        self.timers: Dict[str, float] = {}
        self.histograms: Dict[str, List[float]] = defaultdict(list)
        
        # 系统指标
        self.start_time = datetime.utcnow()
        self.request_count = 0
        self.error_count = 0
        self.total_processing_time = 0.0
    
    def increment(self, name: str, value: float = 1.0, tags: Optional[Dict[str, str]] = None):
        """递增计数器"""
        self.counters[name] += value
        self._add_metric_point(name, self.counters[name], tags or {})
    
    def gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """设置仪表值"""
        self.gauges[name] = value
        self._add_metric_point(name, value, tags or {})
    
    def timer(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """记录时间指标"""
        self.timers[name] = value
        self._add_metric_point(name, value, tags or {})
    
    def histogram(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """记录直方图数据"""
        self.histograms[name].append(value)
        # 保持最近1000个值
        if len(self.histograms[name]) > 1000:
            self.histograms[name] = self.histograms[name][-1000:]
        
        self._add_metric_point(name, value, tags or {})
    
    def _add_metric_point(self, name: str, value: float, tags: Dict[str, str]):
        """添加指标数据点"""
        point = MetricPoint(
            name=name,
            value=value,
            timestamp=datetime.utcnow(),
            tags=tags
        )
        self.metrics[name].append(point)
    
    def get_counter(self, name: str) -> float:
        """获取计数器值"""
        return self.counters.get(name, 0.0)
    
    def get_gauge(self, name: str) -> Optional[float]:
        """获取仪表值"""
        return self.gauges.get(name)
    
    def get_timer(self, name: str) -> Optional[float]:
        """获取计时器值"""
        return self.timers.get(name)
    
    def get_histogram_stats(self, name: str) -> Dict[str, float]:
        """获取直方图统计"""
        values = self.histograms.get(name, [])
        if not values:
            return {}
        
        sorted_values = sorted(values)
        count = len(sorted_values)
        
        return {
            'count': count,
            'min': min(sorted_values),
            'max': max(sorted_values),
            'mean': sum(sorted_values) / count,
            'p50': sorted_values[int(count * 0.5)],
            'p90': sorted_values[int(count * 0.9)],
            'p95': sorted_values[int(count * 0.95)],
            'p99': sorted_values[int(count * 0.99)] if count > 100 else sorted_values[-1],
        }
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        uptime = (datetime.utcnow() - self.start_time).total_seconds()
        
        return {
            'uptime_seconds': uptime,
            'request_count': self.request_count,
            'error_count': self.error_count,
            'error_rate': self.error_count / max(self.request_count, 1),
            'avg_processing_time': self.total_processing_time / max(self.request_count, 1),
            'counters': dict(self.counters),
            'gauges': dict(self.gauges),
            'timers': dict(self.timers),
            'histograms': {name: self.get_histogram_stats(name) for name in self.histograms},
            'timestamp': datetime.utcnow().isoformat(),
        }
    
    def reset(self):
        """重置所有指标"""
        self.metrics.clear()
        self.counters.clear()
        self.gauges.clear()
        self.timers.clear()
        self.histograms.clear()
        self.request_count = 0
        self.error_count = 0
        self.total_processing_time = 0.0
        self.start_time = datetime.utcnow()


class Timer:
    """计时器上下文管理器"""
    
    def __init__(self, metrics_collector: MetricsCollector, name: str, tags: Optional[Dict[str, str]] = None):
        self.metrics_collector = metrics_collector
        self.name = name
        self.tags = tags or {}
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time is not None:
            elapsed = time.time() - self.start_time
            self.metrics_collector.timer(self.name, elapsed, self.tags)
            self.metrics_collector.histogram(f"{self.name}_histogram", elapsed, self.tags)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = MetricsCollector()
        self.active_requests = 0
        self.peak_requests = 0
    
    def start_request(self, request_type: str = "default"):
        """开始请求监控"""
        self.active_requests += 1
        self.peak_requests = max(self.peak_requests, self.active_requests)
        self.metrics.increment(f"requests.{request_type}.started")
        self.metrics.gauge("requests.active", self.active_requests)
        self.metrics.gauge("requests.peak", self.peak_requests)
        
        return Timer(self.metrics, f"requests.{request_type}.duration")
    
    def end_request(self, request_type: str = "default", success: bool = True):
        """结束请求监控"""
        self.active_requests = max(0, self.active_requests - 1)
        self.metrics.gauge("requests.active", self.active_requests)
        
        if success:
            self.metrics.increment(f"requests.{request_type}.success")
        else:
            self.metrics.increment(f"requests.{request_type}.error")
            self.metrics.error_count += 1
        
        self.metrics.request_count += 1
    
    def record_cache_hit(self, cache_type: str = "default"):
        """记录缓存命中"""
        self.metrics.increment(f"cache.{cache_type}.hits")
    
    def record_cache_miss(self, cache_type: str = "default"):
        """记录缓存未命中"""
        self.metrics.increment(f"cache.{cache_type}.misses")
    
    def record_db_query(self, query_type: str, duration: float):
        """记录数据库查询"""
        self.metrics.timer(f"db.{query_type}.duration", duration)
        self.metrics.increment(f"db.{query_type}.count")
    
    def record_ai_request(self, model: str, duration: float, tokens: int):
        """记录AI模型请求"""
        self.metrics.timer(f"ai.{model}.duration", duration)
        self.metrics.increment(f"ai.{model}.requests")
        self.metrics.histogram(f"ai.{model}.tokens", tokens)
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        metrics_summary = self.metrics.get_metrics_summary()
        
        # 简单的健康检查逻辑
        error_rate = metrics_summary.get('error_rate', 0)
        avg_processing_time = metrics_summary.get('avg_processing_time', 0)
        
        if error_rate > 0.1:  # 错误率超过10%
            status = "unhealthy"
        elif error_rate > 0.05 or avg_processing_time > 5.0:  # 错误率超过5%或平均处理时间超过5秒
            status = "degraded"
        else:
            status = "healthy"
        
        return {
            'status': status,
            'error_rate': error_rate,
            'avg_processing_time': avg_processing_time,
            'active_requests': self.active_requests,
            'peak_requests': self.peak_requests,
            'uptime_seconds': metrics_summary['uptime_seconds'],
            'timestamp': datetime.utcnow().isoformat(),
        }


class HealthChecker:
    """健康检查器"""

    def __init__(self):
        self.checks = {}
        self.last_check_time = {}

    def register_check(self, name: str, check_func, interval: int = 60):
        """注册健康检查"""
        self.checks[name] = {
            'func': check_func,
            'interval': interval,
            'last_result': None,
            'last_error': None
        }

    async def run_check(self, name: str) -> Dict[str, Any]:
        """运行单个健康检查"""
        if name not in self.checks:
            return {'status': 'unknown', 'error': 'Check not found'}

        check = self.checks[name]
        now = datetime.utcnow()

        # 检查是否需要运行
        last_check = self.last_check_time.get(name)
        if last_check and (now - last_check).total_seconds() < check['interval']:
            return check['last_result'] or {'status': 'pending'}

        try:
            if asyncio.iscoroutinefunction(check['func']):
                result = await check['func']()
            else:
                result = check['func']()

            check['last_result'] = {
                'status': 'healthy',
                'result': result,
                'timestamp': now.isoformat()
            }
            check['last_error'] = None

        except Exception as e:
            check['last_result'] = {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': now.isoformat()
            }
            check['last_error'] = str(e)

        self.last_check_time[name] = now
        return check['last_result']

    async def run_all_checks(self) -> Dict[str, Any]:
        """运行所有健康检查"""
        results = {}
        for name in self.checks:
            results[name] = await self.run_check(name)

        # 计算总体状态
        statuses = [result.get('status', 'unknown') for result in results.values()]
        if all(status == 'healthy' for status in statuses):
            overall_status = 'healthy'
        elif any(status == 'unhealthy' for status in statuses):
            overall_status = 'unhealthy'
        else:
            overall_status = 'degraded'

        return {
            'overall_status': overall_status,
            'checks': results,
            'timestamp': datetime.utcnow().isoformat()
        }


# 全局监控实例
monitor = PerformanceMonitor()
metrics = monitor.metrics
health_checker = HealthChecker()
