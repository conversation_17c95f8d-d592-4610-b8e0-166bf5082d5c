"""
性能指标工具模块
"""

import time
from typing import Dict, Any
from datetime import datetime


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self.metrics = {}
        self.start_times = {}
    
    def start_timer(self, name: str):
        """开始计时"""
        self.start_times[name] = time.time()
    
    def end_timer(self, name: str) -> float:
        """结束计时并返回耗时"""
        if name in self.start_times:
            elapsed = time.time() - self.start_times[name]
            self.metrics[f"{name}_duration"] = elapsed
            del self.start_times[name]
            return elapsed
        return 0.0
    
    def increment(self, name: str, value: int = 1):
        """递增计数器"""
        self.metrics[name] = self.metrics.get(name, 0) + value
    
    def set_gauge(self, name: str, value: float):
        """设置仪表值"""
        self.metrics[name] = value
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取所有指标"""
        return self.metrics.copy()
    
    def reset(self):
        """重置所有指标"""
        self.metrics.clear()
        self.start_times.clear()


# 全局指标收集器
metrics = MetricsCollector()
