"""
文本处理工具
"""

import re
try:
    import jieba
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False
    print("警告: jieba未安装，将使用简单的文本分割")
from typing import List, Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class TextStats:
    """文本统计信息"""
    char_count: int
    word_count: int
    sentence_count: int
    paragraph_count: int
    language: str


class TextProcessor:
    """文本处理器"""
    
    def __init__(self):
        # 中文标点符号
        self.chinese_punctuation = "！？｡。＂＃＄％＆＇（）＊＋，－／：；＜＝＞＠［＼］＾＿｀｛｜｝～｟｠｢｣､、〃》「」『』【】〔〕〖〗〘〙〚〛〜〝〞〟〰〱〲〳〴〵〶〷〸〹〺〻〼〽〾〿"
        
        # 英文标点符号
        self.english_punctuation = "!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~"
        
        # 所有标点符号
        self.all_punctuation = self.chinese_punctuation + self.english_punctuation
        
        # 法律术语词典（示例）
        self.legal_terms = {
            "合同", "协议", "条款", "违约", "责任", "义务", "权利", "赔偿",
            "终止", "解除", "争议", "仲裁", "诉讼", "管辖", "适用法律",
            "保密", "知识产权", "专利", "商标", "著作权", "侵权"
        }
    
    def clean_text(self, text: str) -> str:
        """
        清理文本
        
        Args:
            text: 原始文本
        
        Returns:
            清理后的文本
        """
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符（保留中英文、数字、基本标点）
        text = re.sub(r'[^\u4e00-\u9fff\w\s' + re.escape(self.all_punctuation) + ']', '', text)
        
        # 标准化引号
        text = text.replace('"', '"').replace('"', '"')
        text = text.replace(''', "'").replace(''', "'")
        
        # 去除首尾空白
        text = text.strip()
        
        return text
    
    def detect_language(self, text: str) -> str:
        """
        检测文本语言
        
        Args:
            text: 文本内容
        
        Returns:
            语言代码 (zh-CN, en-US, etc.)
        """
        if not text:
            return "unknown"
        
        # 统计中文字符数量
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        # 统计英文字符数量
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        
        total_chars = chinese_chars + english_chars
        if total_chars == 0:
            return "unknown"
        
        chinese_ratio = chinese_chars / total_chars
        
        if chinese_ratio > 0.5:
            return "zh-CN"
        elif chinese_ratio < 0.1:
            return "en-US"
        else:
            return "mixed"
    
    def segment_chinese(self, text: str) -> List[str]:
        """
        中文分词

        Args:
            text: 中文文本

        Returns:
            分词结果列表
        """
        if JIEBA_AVAILABLE:
            return list(jieba.cut(text))
        else:
            # 简单的中文分割（按字符）
            return list(text)
    
    def extract_keywords(self, text: str, top_k: int = 10) -> List[str]:
        """
        提取关键词

        Args:
            text: 文本内容
            top_k: 返回前k个关键词

        Returns:
            关键词列表
        """
        if JIEBA_AVAILABLE:
            try:
                import jieba.analyse
                keywords = jieba.analyse.extract_tags(text, topK=top_k, withWeight=False)
                return keywords
            except ImportError:
                pass

        # 简单的关键词提取（基于词频）
        words = re.findall(r'\b\w+\b', text.lower())
        word_freq = {}
        for word in words:
            if len(word) > 2:  # 过滤短词
                word_freq[word] = word_freq.get(word, 0) + 1

        # 按频率排序并返回top_k
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in sorted_words[:top_k]]
    
    def extract_legal_terms(self, text: str) -> List[str]:
        """
        提取法律术语
        
        Args:
            text: 文本内容
        
        Returns:
            法律术语列表
        """
        found_terms = []
        text_lower = text.lower()
        
        for term in self.legal_terms:
            if term in text:
                found_terms.append(term)
        
        return list(set(found_terms))
    
    def get_text_stats(self, text: str) -> TextStats:
        """
        获取文本统计信息
        
        Args:
            text: 文本内容
        
        Returns:
            文本统计信息
        """
        if not text:
            return TextStats(0, 0, 0, 0, "unknown")
        
        # 字符数（不包括空格）
        char_count = len(re.sub(r'\s', '', text))
        
        # 词数
        language = self.detect_language(text)
        if language.startswith("zh"):
            # 中文按字符计算
            word_count = len(re.sub(r'\s', '', text))
        else:
            # 英文按单词计算
            word_count = len(text.split())
        
        # 句子数
        sentence_count = len(re.findall(r'[.!?。！？]+', text))
        
        # 段落数
        paragraph_count = len([p for p in text.split('\n') if p.strip()])
        
        return TextStats(
            char_count=char_count,
            word_count=word_count,
            sentence_count=sentence_count,
            paragraph_count=paragraph_count,
            language=language
        )
    
    def split_sentences(self, text: str) -> List[str]:
        """
        分句
        
        Args:
            text: 文本内容
        
        Returns:
            句子列表
        """
        # 中英文句子分割
        sentences = re.split(r'[.!?。！？]+', text)
        
        # 过滤空句子并去除首尾空白
        sentences = [s.strip() for s in sentences if s.strip()]
        
        return sentences
    
    def normalize_whitespace(self, text: str) -> str:
        """
        标准化空白字符
        
        Args:
            text: 文本内容
        
        Returns:
            标准化后的文本
        """
        # 将所有空白字符替换为单个空格
        text = re.sub(r'\s+', ' ', text)
        
        # 去除首尾空白
        text = text.strip()
        
        return text
    
    def remove_extra_punctuation(self, text: str) -> str:
        """
        移除多余的标点符号
        
        Args:
            text: 文本内容
        
        Returns:
            处理后的文本
        """
        # 移除连续的标点符号
        text = re.sub(r'[' + re.escape(self.all_punctuation) + ']{2,}', '.', text)
        
        return text
