"""
会话缓存管理器
管理用户会话和上下文缓存
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent if Path(__file__).parent.name != "backend" else Path(__file__).parent
sys.path.insert(0, str(project_root))

import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

from ..storage.cache_store import CacheManager
from utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class SessionContext:
    """会话上下文"""
    session_id: str
    user_id: Optional[str]
    conversation_history: List[Dict[str, Any]]
    context_summary: str
    created_at: datetime
    last_active: datetime
    metadata: Dict[str, Any]


@dataclass
class ConversationTurn:
    """对话轮次"""
    turn_id: str
    query: str
    response: str
    timestamp: datetime
    confidence: float
    sources: List[Dict[str, Any]]


class SessionCacheManager:
    """
    会话缓存管理器
    
    功能：
    1. 用户会话管理
    2. 对话历史缓存
    3. 上下文维护
    4. 个性化缓存
    5. 会话清理
    """
    
    def __init__(self, cache_manager: CacheManager):
        self.cache_manager = cache_manager
        self.session_prefix = "session:"
        self.user_sessions_prefix = "user_sessions:"
        self.conversation_prefix = "conversation:"
        
        # 缓存配置
        self.session_ttl = 7200  # 2小时
        self.conversation_ttl = 86400  # 24小时
        self.max_conversation_turns = 50
        self.context_summary_threshold = 10
        
        logger.info("会话缓存管理器初始化完成")
    
    async def create_session(self, user_id: Optional[str] = None, 
                           metadata: Optional[Dict[str, Any]] = None) -> str:
        """创建新会话"""
        try:
            import uuid
            session_id = str(uuid.uuid4())
            
            # 创建会话上下文
            session_context = SessionContext(
                session_id=session_id,
                user_id=user_id,
                conversation_history=[],
                context_summary="",
                created_at=datetime.utcnow(),
                last_active=datetime.utcnow(),
                metadata=metadata or {}
            )
            
            # 存储会话
            session_key = f"{self.session_prefix}{session_id}"
            await self.cache_manager.set(
                session_key,
                asdict(session_context),
                self.session_ttl
            )
            
            # 如果有用户ID，添加到用户会话列表
            if user_id:
                await self._add_user_session(user_id, session_id)
            
            logger.debug(f"会话已创建: {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"创建会话失败: {e}")
            raise
    
    async def get_session(self, session_id: str) -> Optional[SessionContext]:
        """获取会话"""
        try:
            session_key = f"{self.session_prefix}{session_id}"
            session_data = await self.cache_manager.get(session_key)
            
            if session_data:
                # 反序列化会话上下文
                session_context = SessionContext(
                    session_id=session_data['session_id'],
                    user_id=session_data['user_id'],
                    conversation_history=session_data['conversation_history'],
                    context_summary=session_data['context_summary'],
                    created_at=datetime.fromisoformat(session_data['created_at']),
                    last_active=datetime.fromisoformat(session_data['last_active']),
                    metadata=session_data['metadata']
                )
                
                return session_context
            
            return None
            
        except Exception as e:
            logger.error(f"获取会话失败: {session_id}, 错误: {e}")
            return None
    
    async def update_session(self, session_context: SessionContext) -> bool:
        """更新会话"""
        try:
            session_key = f"{self.session_prefix}{session_context.session_id}"
            
            # 更新最后活跃时间
            session_context.last_active = datetime.utcnow()
            
            # 存储更新的会话
            await self.cache_manager.set(
                session_key,
                asdict(session_context),
                self.session_ttl
            )
            
            return True
            
        except Exception as e:
            logger.error(f"更新会话失败: {session_context.session_id}, 错误: {e}")
            return False
    
    async def add_conversation_turn(self, session_id: str, query: str, 
                                  response: str, confidence: float,
                                  sources: List[Dict[str, Any]]) -> bool:
        """添加对话轮次"""
        try:
            # 获取会话
            session_context = await self.get_session(session_id)
            if not session_context:
                logger.warning(f"会话不存在: {session_id}")
                return False
            
            # 创建对话轮次
            import uuid
            turn_id = str(uuid.uuid4())
            
            conversation_turn = ConversationTurn(
                turn_id=turn_id,
                query=query,
                response=response,
                timestamp=datetime.utcnow(),
                confidence=confidence,
                sources=sources
            )
            
            # 添加到会话历史
            session_context.conversation_history.append(asdict(conversation_turn))
            
            # 限制历史长度
            if len(session_context.conversation_history) > self.max_conversation_turns:
                session_context.conversation_history = session_context.conversation_history[-self.max_conversation_turns:]
            
            # 检查是否需要生成上下文摘要
            if len(session_context.conversation_history) >= self.context_summary_threshold:
                await self._update_context_summary(session_context)
            
            # 更新会话
            await self.update_session(session_context)
            
            # 单独存储对话轮次（用于详细查询）
            conversation_key = f"{self.conversation_prefix}{turn_id}"
            await self.cache_manager.set(
                conversation_key,
                asdict(conversation_turn),
                self.conversation_ttl
            )
            
            logger.debug(f"对话轮次已添加: {session_id}, 轮次: {turn_id}")
            return True
            
        except Exception as e:
            logger.error(f"添加对话轮次失败: {session_id}, 错误: {e}")
            return False
    
    async def get_conversation_history(self, session_id: str, 
                                     limit: Optional[int] = None) -> List[ConversationTurn]:
        """获取对话历史"""
        try:
            session_context = await self.get_session(session_id)
            if not session_context:
                return []
            
            history = session_context.conversation_history
            if limit:
                history = history[-limit:]
            
            # 转换为ConversationTurn对象
            conversation_turns = []
            for turn_data in history:
                turn = ConversationTurn(
                    turn_id=turn_data['turn_id'],
                    query=turn_data['query'],
                    response=turn_data['response'],
                    timestamp=datetime.fromisoformat(turn_data['timestamp']),
                    confidence=turn_data['confidence'],
                    sources=turn_data['sources']
                )
                conversation_turns.append(turn)
            
            return conversation_turns
            
        except Exception as e:
            logger.error(f"获取对话历史失败: {session_id}, 错误: {e}")
            return []
    
    async def get_user_sessions(self, user_id: str) -> List[str]:
        """获取用户的所有会话"""
        try:
            user_sessions_key = f"{self.user_sessions_prefix}{user_id}"
            sessions = await self.cache_manager.get_list(user_sessions_key, 0, -1)
            
            # 过滤有效会话
            valid_sessions = []
            for session_id in sessions:
                if isinstance(session_id, str):
                    session = await self.get_session(session_id)
                    if session:
                        valid_sessions.append(session_id)
            
            return valid_sessions
            
        except Exception as e:
            logger.error(f"获取用户会话失败: {user_id}, 错误: {e}")
            return []
    
    async def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        try:
            # 获取会话信息
            session_context = await self.get_session(session_id)
            if not session_context:
                return True
            
            # 删除会话
            session_key = f"{self.session_prefix}{session_id}"
            await self.cache_manager.delete(session_key)
            
            # 删除对话轮次
            for turn_data in session_context.conversation_history:
                conversation_key = f"{self.conversation_prefix}{turn_data['turn_id']}"
                await self.cache_manager.delete(conversation_key)
            
            # 从用户会话列表中移除
            if session_context.user_id:
                await self._remove_user_session(session_context.user_id, session_id)
            
            logger.debug(f"会话已删除: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除会话失败: {session_id}, 错误: {e}")
            return False
    
    async def cleanup_expired_sessions(self) -> int:
        """清理过期会话"""
        try:
            # 获取所有会话键
            session_pattern = f"{self.session_prefix}*"
            count = await self.cache_manager.clear_pattern(session_pattern)
            
            logger.info(f"清理过期会话完成: {count}个会话")
            return count
            
        except Exception as e:
            logger.error(f"清理过期会话失败: {e}")
            return 0
    
    async def get_session_statistics(self) -> Dict[str, Any]:
        """获取会话统计"""
        try:
            # 简单统计实现
            # 生产环境应该维护更详细的统计信息
            
            return {
                'active_sessions': 0,  # 需要实现活跃会话计数
                'total_conversations': 0,  # 需要实现对话总数统计
                'avg_session_duration': 0.0,  # 需要实现平均会话时长
                'session_ttl': self.session_ttl,
                'conversation_ttl': self.conversation_ttl
            }
            
        except Exception as e:
            logger.error(f"获取会话统计失败: {e}")
            return {}
    
    async def _add_user_session(self, user_id: str, session_id: str):
        """添加用户会话"""
        try:
            user_sessions_key = f"{self.user_sessions_prefix}{user_id}"
            await self.cache_manager.push_list(
                user_sessions_key,
                session_id,
                ttl=self.session_ttl * 2  # 用户会话列表保存更长时间
            )
        except Exception as e:
            logger.error(f"添加用户会话失败: {user_id}, {session_id}, 错误: {e}")
    
    async def _remove_user_session(self, user_id: str, session_id: str):
        """移除用户会话"""
        try:
            # Redis列表移除操作比较复杂，这里简化处理
            # 生产环境应该使用更高效的数据结构
            user_sessions_key = f"{self.user_sessions_prefix}{user_id}"
            sessions = await self.cache_manager.get_list(user_sessions_key, 0, -1)
            
            # 过滤掉要删除的会话
            filtered_sessions = [s for s in sessions if s != session_id]
            
            # 重新设置列表
            await self.cache_manager.delete(user_sessions_key)
            if filtered_sessions:
                await self.cache_manager.push_list(
                    user_sessions_key,
                    *filtered_sessions,
                    ttl=self.session_ttl * 2
                )
                
        except Exception as e:
            logger.error(f"移除用户会话失败: {user_id}, {session_id}, 错误: {e}")
    
    async def _update_context_summary(self, session_context: SessionContext):
        """更新上下文摘要"""
        try:
            # 简单的上下文摘要生成
            # 生产环境应该使用LLM生成更智能的摘要
            
            recent_queries = []
            for turn_data in session_context.conversation_history[-5:]:
                recent_queries.append(turn_data['query'])
            
            session_context.context_summary = f"最近讨论的主题包括: {', '.join(recent_queries[:3])}"
            
            logger.debug(f"上下文摘要已更新: {session_context.session_id}")
            
        except Exception as e:
            logger.error(f"更新上下文摘要失败: {session_context.session_id}, 错误: {e}")
