"""
AI模型管理器
统一管理所有AI模型的调用和配置
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent if Path(__file__).parent.name != "backend" else Path(__file__).parent
sys.path.insert(0, str(project_root))

import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any, AsyncGenerator
from dataclasses import dataclass

from .qwen_client import QwenClient, QwenMessage, QwenResponse
from .embedding_client import EmbeddingClient, EmbeddingResponse
from utils.logger import get_logger
from config.settings import get_settings

logger = get_logger(__name__)
settings = get_settings()


@dataclass
class ModelUsage:
    """模型使用统计"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_tokens: int = 0
    total_latency: float = 0.0
    
    @property
    def success_rate(self) -> float:
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests
    
    @property
    def avg_latency(self) -> float:
        if self.successful_requests == 0:
            return 0.0
        return self.total_latency / self.successful_requests


class ModelManager:
    """
    AI模型管理器
    
    负责：
    1. 统一的模型调用接口
    2. 模型负载均衡
    3. 错误处理和重试
    4. 使用统计和监控
    5. 模型健康检查
    """
    
    def __init__(self):
        # 初始化客户端
        self.qwen_client = QwenClient()
        self.embedding_client = EmbeddingClient()
        
        # 使用统计
        self.usage_stats: Dict[str, ModelUsage] = {
            'qwen': ModelUsage(),
            'embedding': ModelUsage()
        }
        
        # 模型配置
        self.model_config = {
            'qwen': {
                'max_retries': 3,
                'retry_delay': 1.0,
                'timeout': 60.0,
                'rate_limit': 100  # 每分钟请求数
            },
            'embedding': {
                'max_retries': 3,
                'retry_delay': 0.5,
                'timeout': 30.0,
                'rate_limit': 200
            }
        }
        
        # 健康状态
        self.health_status: Dict[str, bool] = {
            'qwen': False,
            'embedding': False
        }
        
        logger.info("AI模型管理器初始化完成")
    
    async def initialize(self) -> bool:
        """初始化所有模型客户端"""
        try:
            logger.info("初始化AI模型客户端...")
            
            # 并行健康检查
            health_checks = await asyncio.gather(
                self.qwen_client.health_check(),
                self.embedding_client.health_check(),
                return_exceptions=True
            )
            
            self.health_status['qwen'] = health_checks[0] if not isinstance(health_checks[0], Exception) else False
            self.health_status['embedding'] = health_checks[1] if not isinstance(health_checks[1], Exception) else False
            
            available_models = sum(self.health_status.values())
            logger.info(f"AI模型初始化完成: {available_models}/2 个模型可用")
            
            return available_models > 0
            
        except Exception as e:
            logger.error(f"AI模型初始化失败: {e}")
            return False
    
    async def chat_completion(self, messages: List[QwenMessage],
                            model: Optional[str] = None,
                            temperature: float = 0.7,
                            max_tokens: Optional[int] = None,
                            stream: bool = False) -> QwenResponse:
        """
        聊天完成
        
        Args:
            messages: 消息列表
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            stream: 是否流式响应
            
        Returns:
            QwenResponse: 聊天响应
        """
        start_time = datetime.utcnow()
        self.usage_stats['qwen'].total_requests += 1
        
        try:
            response = await self.qwen_client.chat_completion(
                messages, model, temperature, max_tokens, stream
            )
            
            # 更新统计
            if response.error is None:
                self.usage_stats['qwen'].successful_requests += 1
                self.usage_stats['qwen'].total_tokens += response.usage.get('total_tokens', 0)
                self.usage_stats['qwen'].total_latency += response.latency
            else:
                self.usage_stats['qwen'].failed_requests += 1
            
            return response
            
        except Exception as e:
            self.usage_stats['qwen'].failed_requests += 1
            logger.error(f"聊天完成失败: {e}")
            
            # 返回错误响应
            latency = (datetime.utcnow() - start_time).total_seconds()
            return QwenResponse(
                content=f"聊天完成失败: {str(e)}",
                model=model or self.qwen_client.default_model,
                usage={'prompt_tokens': 0, 'completion_tokens': 0, 'total_tokens': 0},
                finish_reason='error',
                latency=latency,
                error=str(e)
            )
    
    async def generate_text(self, prompt: str,
                          model: Optional[str] = None,
                          temperature: float = 0.7,
                          max_tokens: Optional[int] = None) -> QwenResponse:
        """
        文本生成
        
        Args:
            prompt: 输入提示
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            
        Returns:
            QwenResponse: 生成响应
        """
        messages = [QwenMessage(role='user', content=prompt)]
        return await self.chat_completion(messages, model, temperature, max_tokens)
    
    async def stream_chat(self, messages: List[QwenMessage],
                         model: Optional[str] = None,
                         temperature: float = 0.7,
                         max_tokens: Optional[int] = None) -> AsyncGenerator[str, None]:
        """
        流式聊天
        
        Args:
            messages: 消息列表
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            
        Yields:
            str: 流式文本片段
        """
        self.usage_stats['qwen'].total_requests += 1
        
        try:
            async for chunk in self.qwen_client.stream_chat(
                messages, model, temperature, max_tokens
            ):
                yield chunk
            
            self.usage_stats['qwen'].successful_requests += 1
            
        except Exception as e:
            self.usage_stats['qwen'].failed_requests += 1
            logger.error(f"流式聊天失败: {e}")
            yield f"流式聊天失败: {str(e)}"
    
    async def create_embeddings(self, texts: List[str],
                              model: Optional[str] = None) -> EmbeddingResponse:
        """
        创建文本嵌入
        
        Args:
            texts: 文本列表
            model: 嵌入模型名称
            
        Returns:
            EmbeddingResponse: 嵌入响应
        """
        start_time = datetime.utcnow()
        self.usage_stats['embedding'].total_requests += 1
        
        try:
            response = await self.embedding_client.create_embeddings(texts, model)
            
            # 更新统计
            if response.error is None:
                self.usage_stats['embedding'].successful_requests += 1
                self.usage_stats['embedding'].total_tokens += response.usage.get('total_tokens', 0)
                self.usage_stats['embedding'].total_latency += response.latency
            else:
                self.usage_stats['embedding'].failed_requests += 1
            
            return response
            
        except Exception as e:
            self.usage_stats['embedding'].failed_requests += 1
            logger.error(f"创建嵌入失败: {e}")
            
            # 返回错误响应
            latency = (datetime.utcnow() - start_time).total_seconds()
            return EmbeddingResponse(
                embeddings=[],
                model=model or self.embedding_client.default_model,
                usage={'total_tokens': 0},
                latency=latency,
                error=str(e)
            )
    
    async def batch_create_embeddings(self, texts: List[str],
                                    model: Optional[str] = None,
                                    batch_size: Optional[int] = None) -> EmbeddingResponse:
        """
        批量创建嵌入
        
        Args:
            texts: 文本列表
            model: 嵌入模型名称
            batch_size: 批处理大小
            
        Returns:
            EmbeddingResponse: 嵌入响应
        """
        return await self.embedding_client.batch_create_embeddings(texts, model, batch_size)
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 并行健康检查
            health_checks = await asyncio.gather(
                self.qwen_client.health_check(),
                self.embedding_client.health_check(),
                return_exceptions=True
            )
            
            qwen_healthy = health_checks[0] if not isinstance(health_checks[0], Exception) else False
            embedding_healthy = health_checks[1] if not isinstance(health_checks[1], Exception) else False
            
            self.health_status['qwen'] = qwen_healthy
            self.health_status['embedding'] = embedding_healthy
            
            overall_status = 'healthy' if any(self.health_status.values()) else 'unhealthy'
            
            return {
                'status': overall_status,
                'models': {
                    'qwen': 'ok' if qwen_healthy else 'error',
                    'embedding': 'ok' if embedding_healthy else 'error'
                },
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"AI模型健康检查失败: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    def get_usage_statistics(self) -> Dict[str, Any]:
        """获取使用统计"""
        return {
            'qwen': {
                'total_requests': self.usage_stats['qwen'].total_requests,
                'successful_requests': self.usage_stats['qwen'].successful_requests,
                'failed_requests': self.usage_stats['qwen'].failed_requests,
                'success_rate': self.usage_stats['qwen'].success_rate,
                'total_tokens': self.usage_stats['qwen'].total_tokens,
                'avg_latency': self.usage_stats['qwen'].avg_latency
            },
            'embedding': {
                'total_requests': self.usage_stats['embedding'].total_requests,
                'successful_requests': self.usage_stats['embedding'].successful_requests,
                'failed_requests': self.usage_stats['embedding'].failed_requests,
                'success_rate': self.usage_stats['embedding'].success_rate,
                'total_tokens': self.usage_stats['embedding'].total_tokens,
                'avg_latency': self.usage_stats['embedding'].avg_latency
            }
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'qwen': self.qwen_client.get_model_info(),
            'embedding': self.embedding_client.get_model_info(),
            'health_status': self.health_status,
            'usage_stats': self.get_usage_statistics()
        }
    
    def clear_caches(self):
        """清空所有缓存"""
        self.embedding_client.clear_cache()
        logger.info("AI模型缓存已清空")
    
    async def shutdown(self):
        """关闭模型管理器"""
        try:
            # 清理资源
            self.clear_caches()
            logger.info("AI模型管理器已关闭")
        except Exception as e:
            logger.error(f"关闭AI模型管理器失败: {e}")
