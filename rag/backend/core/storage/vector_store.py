"""
ChromaDB向量存储管理器
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent if Path(__file__).parent.name != "backend" else Path(__file__).parent
sys.path.insert(0, str(project_root))

import asyncio
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple

try:
    import chromadb
    from chromadb.config import Settings
    from chromadb.api.models.Collection import Collection
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False

import numpy as np
from utils.logger import get_logger
from config.settings import get_settings

logger = get_logger(__name__)
settings = get_settings()


class VectorStoreManager:
    """
    ChromaDB向量存储管理器
    
    负责：
    1. 向量数据库连接管理
    2. 文档向量存储和检索
    3. 相似度搜索
    4. 向量索引管理
    5. 集合管理
    """
    
    def __init__(self, collection_name: str = "rag_documents"):
        self.client = None
        self.collection: Optional[Collection] = None
        self.collection_name = collection_name
        self.connected = False
        self.embedding_dimension = settings.embedding_dimensions
        
        logger.info(f"向量存储管理器初始化: 集合={collection_name}")
    
    async def connect(self) -> bool:
        """连接ChromaDB"""
        if not CHROMADB_AVAILABLE:
            logger.warning("chromadb未安装，使用模拟向量存储")
            self.connected = True
            return True
        
        try:
            logger.info("连接ChromaDB...")
            
            # 创建ChromaDB客户端
            if hasattr(settings, 'chroma_host') and settings.chroma_host:
                # 连接远程ChromaDB
                self.client = chromadb.HttpClient(
                    host=settings.chroma_host,
                    port=settings.chroma_port,
                    settings=Settings(
                        chroma_client_auth_provider="chromadb.auth.basic.BasicAuthClientProvider",
                        chroma_client_auth_credentials_provider="chromadb.auth.basic.BasicAuthCredentialsProvider"
                    )
                )
            else:
                # 使用本地持久化存储
                self.client = chromadb.PersistentClient(
                    path="./data/chroma_db",
                    settings=Settings(
                        anonymized_telemetry=False,
                        allow_reset=True
                    )
                )
            
            # 获取或创建集合
            try:
                self.collection = self.client.get_collection(
                    name=self.collection_name,
                    embedding_function=None  # 我们将手动管理嵌入
                )
                logger.info(f"已连接到现有集合: {self.collection_name}")
            except Exception:
                # 集合不存在，创建新集合
                self.collection = self.client.create_collection(
                    name=self.collection_name,
                    embedding_function=None,
                    metadata={"description": "RAG系统文档向量存储"}
                )
                logger.info(f"已创建新集合: {self.collection_name}")
            
            self.connected = True
            logger.info("ChromaDB连接成功")
            return True
            
        except Exception as e:
            logger.error(f"ChromaDB连接失败: {e}")
            # 在开发环境下使用模拟模式
            if settings.debug:
                logger.info("开发环境：启用向量存储模拟模式")
                self.connected = True
                return True
            return False
    
    async def disconnect(self):
        """断开连接"""
        try:
            if self.client:
                # ChromaDB客户端通常不需要显式关闭
                pass
            self.connected = False
            logger.info("向量存储连接已关闭")
        except Exception as e:
            logger.error(f"关闭向量存储连接失败: {e}")
    
    async def add_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """批量添加文档向量"""
        try:
            if not self.connected:
                raise RuntimeError("向量存储未连接")
            
            if not documents:
                return True
            
            if not self.collection:
                # 模拟模式
                logger.debug(f"模拟添加 {len(documents)} 个文档向量")
                return True
            
            # 准备数据
            ids = []
            embeddings = []
            metadatas = []
            documents_text = []
            
            for doc in documents:
                ids.append(doc['id'])
                embeddings.append(doc['embedding'])
                metadatas.append(doc.get('metadata', {}))
                documents_text.append(doc.get('content', ''))
            
            # 批量添加到ChromaDB
            self.collection.add(
                ids=ids,
                embeddings=embeddings,
                metadatas=metadatas,
                documents=documents_text
            )
            
            logger.debug(f"已添加 {len(documents)} 个文档向量")
            return True
            
        except Exception as e:
            logger.error(f"添加文档向量失败: {e}")
            return False
    
    async def search_similar(self, query_embedding: List[float], 
                           top_k: int = 10, 
                           filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """相似度搜索"""
        try:
            if not self.connected:
                return []
            
            if not self.collection:
                # 模拟搜索结果
                return self._generate_mock_results(top_k)
            
            # 构建查询条件
            where_clause = None
            if filters:
                where_clause = {}
                for key, value in filters.items():
                    where_clause[key] = {"$eq": value}
            
            # 执行相似度搜索
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                where=where_clause,
                include=['documents', 'metadatas', 'distances']
            )
            
            # 转换结果格式
            search_results = []
            if results['ids'] and len(results['ids']) > 0:
                for i in range(len(results['ids'][0])):
                    result = {
                        'id': results['ids'][0][i],
                        'content': results['documents'][0][i] if results['documents'] else '',
                        'metadata': results['metadatas'][0][i] if results['metadatas'] else {},
                        'distance': results['distances'][0][i] if results['distances'] else 0.0,
                        'score': 1.0 - results['distances'][0][i] if results['distances'] else 1.0
                    }
                    search_results.append(result)
            
            logger.debug(f"相似度搜索完成: 返回 {len(search_results)} 个结果")
            return search_results
            
        except Exception as e:
            logger.error(f"相似度搜索失败: {e}")
            return []
    
    async def delete_documents(self, document_ids: List[str]) -> bool:
        """删除文档向量"""
        try:
            if not self.connected or not document_ids:
                return True
            
            if not self.collection:
                logger.debug(f"模拟删除 {len(document_ids)} 个文档向量")
                return True
            
            # 从ChromaDB删除
            self.collection.delete(ids=document_ids)
            
            logger.debug(f"已删除 {len(document_ids)} 个文档向量")
            return True
            
        except Exception as e:
            logger.error(f"删除文档向量失败: {e}")
            return False
    
    async def update_document(self, document_id: str, embedding: List[float],
                            content: str, metadata: Dict[str, Any]) -> bool:
        """更新文档向量"""
        try:
            if not self.connected:
                return True
            
            if not self.collection:
                logger.debug(f"模拟更新文档向量: {document_id}")
                return True
            
            # ChromaDB的更新操作
            self.collection.upsert(
                ids=[document_id],
                embeddings=[embedding],
                documents=[content],
                metadatas=[metadata]
            )
            
            logger.debug(f"文档向量已更新: {document_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新文档向量失败: {e}")
            return False
    
    async def get_document(self, document_id: str) -> Optional[Dict[str, Any]]:
        """获取文档向量"""
        try:
            if not self.connected:
                return None
            
            if not self.collection:
                return None
            
            # 从ChromaDB获取文档
            results = self.collection.get(
                ids=[document_id],
                include=['documents', 'metadatas', 'embeddings']
            )
            
            if results['ids'] and len(results['ids']) > 0:
                return {
                    'id': results['ids'][0],
                    'content': results['documents'][0] if results['documents'] else '',
                    'metadata': results['metadatas'][0] if results['metadatas'] else {},
                    'embedding': results['embeddings'][0] if results['embeddings'] else []
                }
            
            return None
            
        except Exception as e:
            logger.error(f"获取文档向量失败: {e}")
            return None
    
    async def list_collections(self) -> List[str]:
        """列出所有集合"""
        try:
            if not self.connected or not self.client:
                return []
            
            collections = self.client.list_collections()
            return [col.name for col in collections]
            
        except Exception as e:
            logger.error(f"列出集合失败: {e}")
            return []
    
    async def get_collection_info(self) -> Dict[str, Any]:
        """获取集合信息"""
        try:
            if not self.connected or not self.collection:
                return {
                    'name': self.collection_name,
                    'count': 0,
                    'metadata': {}
                }
            
            count = self.collection.count()
            metadata = self.collection.metadata or {}
            
            return {
                'name': self.collection_name,
                'count': count,
                'metadata': metadata
            }
            
        except Exception as e:
            logger.error(f"获取集合信息失败: {e}")
            return {}
    
    async def clear_collection(self) -> bool:
        """清空集合"""
        try:
            if not self.connected or not self.collection:
                return True
            
            # 获取所有文档ID并删除
            all_docs = self.collection.get(include=[])
            if all_docs['ids']:
                self.collection.delete(ids=all_docs['ids'])
            
            logger.info(f"集合已清空: {self.collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"清空集合失败: {e}")
            return False
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.connected:
                return False
            
            if not self.collection:
                return True  # 模拟模式
            
            # 尝试获取集合信息
            self.collection.count()
            return True
            
        except Exception as e:
            logger.error(f"向量存储健康检查失败: {e}")
            return False
    
    def _generate_mock_results(self, top_k: int) -> List[Dict[str, Any]]:
        """生成模拟搜索结果"""
        results = []
        for i in range(min(top_k, 5)):  # 最多返回5个模拟结果
            result = {
                'id': f"mock_doc_{i}",
                'content': f"这是模拟的搜索结果 {i+1}，包含与查询相关的内容。",
                'metadata': {
                    'source': f'mock_document_{i+1}.pdf',
                    'chunk_index': i,
                    'document_type': 'pdf'
                },
                'distance': 0.1 + i * 0.1,
                'score': 0.9 - i * 0.1
            }
            results.append(result)
        
        return results
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取向量存储统计信息"""
        try:
            if not self.connected:
                return {
                    'total_vectors': 0,
                    'collection_count': 0,
                    'embedding_dimension': self.embedding_dimension
                }
            
            if not self.collection:
                return {
                    'total_vectors': 0,
                    'collection_count': 1,
                    'embedding_dimension': self.embedding_dimension
                }
            
            vector_count = self.collection.count()
            collections = await self.list_collections()
            
            return {
                'total_vectors': vector_count,
                'collection_count': len(collections),
                'embedding_dimension': self.embedding_dimension,
                'current_collection': self.collection_name
            }
            
        except Exception as e:
            logger.error(f"获取向量存储统计失败: {e}")
            return {}
