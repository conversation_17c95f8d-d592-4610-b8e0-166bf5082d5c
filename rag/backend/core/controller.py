"""
RAG控制器 - 整体流程编排和API接口管理
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent if Path(__file__).parent.name != "backend" else Path(__file__).parent
sys.path.insert(0, str(project_root))

import asyncio
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from models.query import QueryRequest, QueryResponse
from models.response import RAGResponse, RetrievalResult, GenerationResult
from utils.logger import get_logger
from .pipeline import RAGPipeline
from .knowledge import KnowledgeManager
from .retrieval import RetrievalEngine
from .generation import GenerationEngine
from .storage.cache_store import CacheManager
from .cache.query_cache import QueryCacheManager
from .cache.session_cache import SessionCacheManager

logger = get_logger(__name__)


@dataclass
class RAGConfig:
    """RAG配置"""
    max_context_length: int = 4000
    retrieval_top_k: int = 10
    enable_cache: bool = True
    enable_hallucination_detection: bool = True
    enable_citation_generation: bool = True
    cache_ttl: int = 3600  # 缓存过期时间(秒)


class RAGController:
    """
    RAG控制器 - 负责整体流程编排和API接口管理
    
    主要职责：
    1. 工作流编排 - 协调各个组件的执行
    2. 缓存管理 - 查询结果缓存和失效策略
    3. 性能监控 - 延迟、吞吐量、准确率监控
    4. 错误处理 - 异常捕获和恢复机制
    5. API网关 - 统一的外部接口
    """
    
    def __init__(self, config: Optional[RAGConfig] = None):
        self.config = config or RAGConfig()
        self.pipeline = RAGPipeline(self.config)
        self.knowledge_manager = KnowledgeManager()
        self.retrieval_engine = RetrievalEngine()
        self.generation_engine = GenerationEngine()
        self.cache_manager = CacheManager()
        self.query_cache = QueryCacheManager(self.cache_manager)
        self.session_cache = SessionCacheManager(self.cache_manager)

        # 初始化状态
        self.initialized = False

        # 性能监控指标
        self.metrics = {
            'total_queries': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'avg_latency': 0.0,
            'error_count': 0
        }

        logger.info("RAG控制器初始化完成")

    async def initialize(self) -> bool:
        """初始化RAG控制器和所有组件"""
        try:
            logger.info("开始初始化RAG控制器组件...")

            # 初始化缓存管理器
            cache_success = await self.cache_manager.connect()
            if not cache_success:
                logger.warning("缓存管理器初始化失败")

            # 初始化知识库管理器
            knowledge_success = await self.knowledge_manager.initialize()
            if not knowledge_success:
                logger.warning("知识库管理器初始化失败")

            # 初始化检索引擎
            retrieval_success = await self.retrieval_engine.build_indexes([])
            if not retrieval_success:
                logger.warning("检索引擎初始化失败")

            # 初始化生成引擎
            generation_success = await self.generation_engine.initialize()
            if not generation_success:
                logger.warning("生成引擎初始化失败")

            self.initialized = True
            logger.info("RAG控制器组件初始化完成")
            return True

        except Exception as e:
            logger.error(f"RAG控制器初始化失败: {e}")
            return False

    async def shutdown(self):
        """关闭RAG控制器和所有组件"""
        try:
            await self.cache_manager.disconnect()
            await self.knowledge_manager.shutdown()
            await self.generation_engine.shutdown()
            logger.info("RAG控制器已关闭")
        except Exception as e:
            logger.error(f"关闭RAG控制器失败: {e}")

    async def query(self, request: QueryRequest) -> QueryResponse:
        """
        处理RAG查询请求
        
        Args:
            request: 查询请求
            
        Returns:
            QueryResponse: 查询响应
        """
        query_id = str(uuid.uuid4())
        start_time = datetime.utcnow()
        
        try:
            logger.info(f"开始处理RAG查询: {query_id}, 查询: {request.query[:100]}...")
            
            # 更新指标
            self.metrics['total_queries'] += 1
            
            # 检查缓存
            if self.config.enable_cache:
                cached_result = await self.query_cache.get_cached_response(request)
                if cached_result:
                    self.metrics['cache_hits'] += 1
                    logger.info(f"缓存命中: {query_id}")

                    # 更新缓存响应的查询ID和时间戳
                    cached_result.query_id = query_id
                    cached_result.processing_time = (datetime.utcnow() - start_time).total_seconds()
                    cached_result.from_cache = True

                    return cached_result
                else:
                    self.metrics['cache_misses'] += 1
            
            # 执行RAG流水线
            rag_result = await self.pipeline.process(request)
            
            # 设置缓存
            if self.config.enable_cache:
                await self.query_cache.cache_response(request, response)
            
            # 构建响应
            response = self._build_response(query_id, rag_result, start_time)
            
            # 更新性能指标
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            self._update_latency_metric(processing_time)
            
            logger.info(f"RAG查询完成: {query_id}, 耗时: {processing_time:.2f}s")
            return response
            
        except Exception as e:
            self.metrics['error_count'] += 1
            logger.error(f"RAG查询失败: {query_id}, 错误: {str(e)}")
            
            # 返回错误响应
            return QueryResponse(
                query_id=query_id,
                success=False,
                error=str(e),
                processing_time=(datetime.utcnow() - start_time).total_seconds()
            )
    
    async def add_document(self, document_path: str, metadata: Optional[Dict] = None) -> bool:
        """
        添加文档到知识库
        
        Args:
            document_path: 文档路径
            metadata: 文档元数据
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info(f"开始添加文档: {document_path}")
            result = await self.knowledge_manager.add_document(document_path, metadata)
            logger.info(f"文档添加{'成功' if result else '失败'}: {document_path}")
            return result
        except Exception as e:
            logger.error(f"添加文档失败: {document_path}, 错误: {str(e)}")
            return False
    
    async def remove_document(self, document_id: str) -> bool:
        """
        从知识库移除文档
        
        Args:
            document_id: 文档ID
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info(f"开始移除文档: {document_id}")
            result = await self.knowledge_manager.remove_document(document_id)
            
            # 清理相关缓存
            await self.query_cache.invalidate_cache()
            
            logger.info(f"文档移除{'成功' if result else '失败'}: {document_id}")
            return result
        except Exception as e:
            logger.error(f"移除文档失败: {document_id}, 错误: {str(e)}")
            return False
    
    async def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        # 获取缓存统计
        cache_stats = await self.cache_manager.get_statistics()
        query_cache_stats = await self.query_cache.get_cache_statistics()

        # 获取知识库统计
        kb_stats = await self.knowledge_manager.get_statistics()

        return {
            **self.metrics,
            'cache_hit_rate': self.metrics['cache_hits'] / max(self.metrics['total_queries'], 1),
            'error_rate': self.metrics['error_count'] / max(self.metrics['total_queries'], 1),
            'knowledge_base_size': kb_stats.get('document_count', 0),
            'cache_size': cache_stats.get('total_keys', 0),
            'cache_type': cache_stats.get('cache_type', 'unknown'),
            'query_cache': query_cache_stats
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查各组件状态
            knowledge_status = await self.knowledge_manager.health_check()
            retrieval_status = await self.retrieval_engine.health_check()
            generation_status = await self.generation_engine.health_check()
            
            overall_status = all([knowledge_status, retrieval_status, generation_status])
            
            return {
                'status': 'healthy' if overall_status else 'unhealthy',
                'components': {
                    'knowledge_manager': 'ok' if knowledge_status else 'error',
                    'retrieval_engine': 'ok' if retrieval_status else 'error',
                    'generation_engine': 'ok' if generation_status else 'error'
                },
                'timestamp': datetime.utcnow().isoformat()
            }
        except Exception as e:
            logger.error(f"健康检查失败: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    def _generate_cache_key(self, query: str) -> str:
        """生成缓存键"""
        import hashlib
        return f"query:{hashlib.md5(query.encode()).hexdigest()}"
    
    def _build_response(self, query_id: str, rag_result: RAGResponse, 
                       start_time: datetime, from_cache: bool = False) -> QueryResponse:
        """构建查询响应"""
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        return QueryResponse(
            query_id=query_id,
            success=True,
            response=rag_result.response,
            sources=rag_result.sources,
            confidence=rag_result.confidence,
            processing_time=processing_time,
            from_cache=from_cache,
            metadata={
                'retrieval_count': len(rag_result.sources),
                'context_length': len(rag_result.context) if hasattr(rag_result, 'context') else 0
            }
        )
    
    def _update_latency_metric(self, latency: float):
        """更新延迟指标"""
        if self.metrics['avg_latency'] == 0:
            self.metrics['avg_latency'] = latency
        else:
            # 简单的移动平均
            self.metrics['avg_latency'] = (self.metrics['avg_latency'] * 0.9 + latency * 0.1)
