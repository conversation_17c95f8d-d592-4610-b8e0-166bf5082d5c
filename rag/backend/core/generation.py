"""
生成增强模块 - 上下文构建和LLM调用
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent if Path(__file__).parent.name != "backend" else Path(__file__).parent
sys.path.insert(0, str(project_root))

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from models.response import RetrievalResult, GenerationResult
from utils.logger import get_logger
from .ai_models.model_manager import ModelManager
from .ai_models.qwen_client import QwenMessage

logger = get_logger(__name__)


class TaskType(Enum):
    """任务类型"""
    GENERAL_QA = "general_qa"
    RISK_ASSESSMENT = "risk_assessment"
    CONTRACT_ANALYSIS = "contract_analysis"
    SUMMARIZATION = "summarization"
    RECOMMENDATION = "recommendation"


@dataclass
class PromptTemplate:
    """提示模板"""
    name: str
    template: str
    variables: List[str]
    task_type: TaskType
    max_tokens: int = 2000


@dataclass
class GenerationConfig:
    """生成配置"""
    max_context_length: int = 4000
    max_response_tokens: int = 2000
    temperature: float = 0.7
    top_p: float = 0.9
    enable_streaming: bool = False
    enable_hallucination_detection: bool = True
    enable_citation_generation: bool = True


class ContextBuilder:
    """上下文构建器"""
    
    def __init__(self, max_length: int = 4000):
        self.max_length = max_length
        logger.debug("上下文构建器初始化完成")
    
    def build_context(self, query: str, retrieval_results: List[RetrievalResult], 
                     task_type: TaskType = TaskType.GENERAL_QA) -> Dict[str, Any]:
        """
        构建生成上下文
        
        Args:
            query: 用户查询
            retrieval_results: 检索结果
            task_type: 任务类型
            
        Returns:
            Dict: 构建的上下文
        """
        try:
            logger.debug(f"开始构建上下文: 任务类型={task_type.value}")
            
            # 按相关性排序
            sorted_results = sorted(retrieval_results, key=lambda x: x.score, reverse=True)
            
            # 构建上下文片段
            context_parts = []
            total_length = 0
            used_sources = []
            
            for i, result in enumerate(sorted_results):
                # 格式化上下文片段
                context_part = self._format_context_part(result, i + 1)
                part_length = len(context_part)
                
                # 检查长度限制
                if total_length + part_length <= self.max_length:
                    context_parts.append(context_part)
                    total_length += part_length
                    used_sources.append(result)
                else:
                    # 尝试截断最后一个片段
                    remaining_length = self.max_length - total_length
                    if remaining_length > 100:  # 至少保留100字符
                        truncated_content = result.content[:remaining_length-50] + "..."
                        truncated_part = self._format_context_part(
                            RetrievalResult(
                                document_id=result.document_id,
                                content=truncated_content,
                                score=result.score,
                                metadata=result.metadata
                            ), i + 1
                        )
                        context_parts.append(truncated_part)
                        used_sources.append(result)
                    break
            
            # 组装最终上下文
            context_text = "\n\n".join(context_parts)
            
            context_info = {
                'context_text': context_text,
                'context_length': len(context_text),
                'source_count': len(used_sources),
                'used_sources': used_sources,
                'task_type': task_type,
                'query': query
            }
            
            logger.debug(f"上下文构建完成: 长度={len(context_text)}, 源数量={len(used_sources)}")
            return context_info
            
        except Exception as e:
            logger.error(f"上下文构建失败: {str(e)}")
            return {
                'context_text': '',
                'context_length': 0,
                'source_count': 0,
                'used_sources': [],
                'task_type': task_type,
                'query': query
            }
    
    def _format_context_part(self, result: RetrievalResult, index: int) -> str:
        """格式化上下文片段"""
        source_info = result.metadata.get('source', f'文档{index}')
        return f"[来源{index}: {source_info}]\n{result.content}"


class PromptManager:
    """提示管理器"""
    
    def __init__(self):
        self.templates = self._load_templates()
        logger.debug("提示管理器初始化完成")
    
    def get_prompt(self, task_type: TaskType, context_info: Dict[str, Any]) -> str:
        """
        获取任务提示
        
        Args:
            task_type: 任务类型
            context_info: 上下文信息
            
        Returns:
            str: 构建的提示
        """
        template = self.templates.get(task_type)
        if not template:
            logger.warning(f"未找到任务类型 {task_type.value} 的模板，使用通用模板")
            template = self.templates[TaskType.GENERAL_QA]
        
        try:
            # 构建提示变量
            variables = {
                'query': context_info['query'],
                'context': context_info['context_text'],
                'source_count': context_info['source_count']
            }
            
            # 格式化提示
            prompt = template.template.format(**variables)
            
            logger.debug(f"提示构建完成: 任务类型={task_type.value}, 长度={len(prompt)}")
            return prompt
            
        except Exception as e:
            logger.error(f"提示构建失败: {str(e)}")
            return f"请根据以下信息回答问题：\n\n问题：{context_info['query']}\n\n参考信息：\n{context_info['context_text']}"
    
    def _load_templates(self) -> Dict[TaskType, PromptTemplate]:
        """加载提示模板"""
        templates = {
            TaskType.GENERAL_QA: PromptTemplate(
                name="通用问答",
                template="""你是一个专业的AI助手，请根据提供的参考信息回答用户的问题。

问题：{query}

参考信息（共{source_count}个来源）：
{context}

请基于上述参考信息，准确、详细地回答问题。如果参考信息不足以回答问题，请明确说明。请在回答中适当引用相关来源。""",
                variables=['query', 'context', 'source_count'],
                task_type=TaskType.GENERAL_QA
            ),
            
            TaskType.RISK_ASSESSMENT: PromptTemplate(
                name="风险评估",
                template="""你是一位专业的法律风险评估师，请根据提供的合同信息进行风险分析。

分析请求：{query}

合同相关信息（共{source_count}个来源）：
{context}

请从以下维度进行风险评估：
1. 法律风险 - 条款是否符合法律法规
2. 商业风险 - 商业条款是否合理
3. 操作风险 - 执行过程中可能遇到的问题
4. 财务风险 - 付款、担保等财务相关风险

对每个风险类别，请提供：
- 风险等级（低/中/高/严重）
- 风险描述
- 可能的影响
- 建议的缓解措施

请确保分析基于提供的合同信息，并明确引用相关条款。""",
                variables=['query', 'context', 'source_count'],
                task_type=TaskType.RISK_ASSESSMENT
            ),
            
            TaskType.CONTRACT_ANALYSIS: PromptTemplate(
                name="合同分析",
                template="""你是一位专业的合同分析师，请对提供的合同内容进行详细分析。

分析要求：{query}

合同内容（共{source_count}个来源）：
{context}

请从以下方面进行分析：
1. 合同基本信息（当事方、合同性质、期限等）
2. 核心条款分析（权利义务、履行方式、违约责任等）
3. 特殊条款识别（免责条款、争议解决条款等）
4. 合同完整性评估（是否存在缺失或模糊的条款）
5. 合规性检查（是否符合相关法律法规）

请确保分析准确、全面，并在适当位置引用具体的合同条款。""",
                variables=['query', 'context', 'source_count'],
                task_type=TaskType.CONTRACT_ANALYSIS
            ),
            
            TaskType.SUMMARIZATION: PromptTemplate(
                name="内容总结",
                template="""请对以下内容进行专业的总结。

总结要求：{query}

待总结内容（共{source_count}个来源）：
{context}

请提供：
1. 核心要点总结（3-5个主要点）
2. 详细内容概述
3. 关键信息提取
4. 结论或建议（如适用）

总结应当准确、简洁、结构清晰，突出重点信息。""",
                variables=['query', 'context', 'source_count'],
                task_type=TaskType.SUMMARIZATION
            ),
            
            TaskType.RECOMMENDATION: PromptTemplate(
                name="建议生成",
                template="""基于提供的信息，请生成专业的建议和优化方案。

建议请求：{query}

参考信息（共{source_count}个来源）：
{context}

请提供：
1. 现状分析 - 基于参考信息的现状评估
2. 问题识别 - 发现的主要问题或改进点
3. 具体建议 - 详细的改进建议和实施方案
4. 优先级排序 - 建议的重要性和紧急程度
5. 预期效果 - 实施建议后的预期改善

建议应当具体可操作，有助于解决实际问题。""",
                variables=['query', 'context', 'source_count'],
                task_type=TaskType.RECOMMENDATION
            )
        }
        
        return templates


class QualityController:
    """质量控制器"""
    
    def __init__(self):
        logger.debug("质量控制器初始化完成")
    
    async def validate_response(self, response: str, context: str, 
                              query: str) -> Dict[str, Any]:
        """
        验证响应质量
        
        Args:
            response: 生成的响应
            context: 参考上下文
            query: 原始查询
            
        Returns:
            Dict: 质量评估结果
        """
        try:
            logger.debug("开始响应质量验证")
            
            # 幻觉检测
            hallucination_score = await self._detect_hallucination(response, context)
            
            # 相关性评估
            relevance_score = self._assess_relevance(response, query)
            
            # 完整性评估
            completeness_score = self._assess_completeness(response, query)
            
            # 一致性检查
            consistency_score = self._check_consistency(response, context)
            
            # 综合质量分数
            overall_score = (
                relevance_score * 0.3 +
                completeness_score * 0.25 +
                consistency_score * 0.25 +
                (1 - hallucination_score) * 0.2
            )
            
            quality_result = {
                'overall_score': overall_score,
                'relevance_score': relevance_score,
                'completeness_score': completeness_score,
                'consistency_score': consistency_score,
                'hallucination_score': hallucination_score,
                'passed': overall_score >= 0.7 and hallucination_score <= 0.3
            }
            
            logger.debug(f"质量验证完成: 总分={overall_score:.2f}, 通过={'是' if quality_result['passed'] else '否'}")
            return quality_result
            
        except Exception as e:
            logger.error(f"质量验证失败: {str(e)}")
            return {
                'overall_score': 0.5,
                'relevance_score': 0.5,
                'completeness_score': 0.5,
                'consistency_score': 0.5,
                'hallucination_score': 0.5,
                'passed': False
            }
    
    async def _detect_hallucination(self, response: str, context: str) -> float:
        """检测幻觉"""
        # 简单的幻觉检测实现
        # 生产环境应使用专门的幻觉检测模型
        
        # 检查响应中的关键信息是否在上下文中
        response_words = set(response.lower().split())
        context_words = set(context.lower().split())
        
        # 计算重叠度
        overlap = len(response_words.intersection(context_words))
        total_response_words = len(response_words)
        
        if total_response_words == 0:
            return 1.0  # 空响应视为高幻觉
        
        overlap_ratio = overlap / total_response_words
        hallucination_score = max(0.0, 1.0 - overlap_ratio * 2)  # 简单的启发式计算
        
        return min(hallucination_score, 1.0)
    
    def _assess_relevance(self, response: str, query: str) -> float:
        """评估相关性"""
        # 简单的相关性评估
        query_words = set(query.lower().split())
        response_words = set(response.lower().split())
        
        if not query_words:
            return 0.5
        
        overlap = len(query_words.intersection(response_words))
        relevance_score = overlap / len(query_words)
        
        return min(relevance_score, 1.0)
    
    def _assess_completeness(self, response: str, query: str) -> float:
        """评估完整性"""
        # 基于响应长度的简单完整性评估
        min_length = 50  # 最小期望长度
        optimal_length = 500  # 最优长度
        
        response_length = len(response)
        
        if response_length < min_length:
            return response_length / min_length
        elif response_length <= optimal_length:
            return 1.0
        else:
            # 过长的响应可能包含冗余信息
            return max(0.7, 1.0 - (response_length - optimal_length) / optimal_length)
    
    def _check_consistency(self, response: str, context: str) -> float:
        """检查一致性"""
        # 简单的一致性检查
        # 生产环境应使用更复杂的语义一致性检查
        
        # 检查是否存在明显的矛盾
        contradiction_indicators = ['但是', '然而', '相反', '不过', '却']
        contradiction_count = sum(1 for indicator in contradiction_indicators if indicator in response)
        
        # 基于矛盾指示词的数量评估一致性
        max_contradictions = 3
        consistency_score = max(0.0, 1.0 - contradiction_count / max_contradictions)
        
        return consistency_score


class GenerationEngine:
    """
    生成增强引擎 - 负责上下文构建和LLM调用
    
    主要职责：
    1. 智能上下文构建
    2. 提示模板管理
    3. Token优化管理
    4. 结果验证和质量控制
    5. 引用来源生成
    """
    
    def __init__(self, config: Optional[GenerationConfig] = None):
        self.config = config or GenerationConfig()

        # 初始化组件
        self.context_builder = ContextBuilder(self.config.max_context_length)
        self.prompt_manager = PromptManager()
        self.quality_controller = QualityController()
        self.model_manager = ModelManager()

        # 初始化状态
        self.initialized = False

        # 生成统计
        self.generation_stats = {
            'total_requests': 0,
            'successful_generations': 0,
            'failed_generations': 0,
            'quality_passed': 0,
            'quality_failed': 0
        }

        logger.info("生成增强引擎初始化完成")

    async def initialize(self) -> bool:
        """初始化生成引擎"""
        try:
            # 初始化AI模型管理器
            success = await self.model_manager.initialize()
            self.initialized = success

            if success:
                logger.info("生成引擎初始化成功")
            else:
                logger.warning("生成引擎初始化失败，将使用模拟模式")

            return True  # 即使AI模型不可用，也可以使用模拟模式

        except Exception as e:
            logger.error(f"生成引擎初始化失败: {e}")
            return False

    async def shutdown(self):
        """关闭生成引擎"""
        try:
            await self.model_manager.shutdown()
            logger.info("生成引擎已关闭")
        except Exception as e:
            logger.error(f"关闭生成引擎失败: {e}")

    async def generate(self, query: str, retrieval_results: List[RetrievalResult],
                      task_type: TaskType = TaskType.GENERAL_QA) -> GenerationResult:
        """
        生成增强响应
        
        Args:
            query: 用户查询
            retrieval_results: 检索结果
            task_type: 任务类型
            
        Returns:
            GenerationResult: 生成结果
        """
        start_time = datetime.utcnow()
        self.generation_stats['total_requests'] += 1
        
        try:
            logger.info(f"开始生成响应: 任务类型={task_type.value}")
            
            # 1. 构建上下文
            context_info = self.context_builder.build_context(
                query, retrieval_results, task_type
            )
            
            # 2. 构建提示
            prompt = self.prompt_manager.get_prompt(task_type, context_info)
            
            # 3. 调用LLM生成
            response_text = await self._call_llm(prompt, task_type)
            
            # 4. 质量控制
            if self.config.enable_hallucination_detection:
                quality_result = await self.quality_controller.validate_response(
                    response_text, context_info['context_text'], query
                )
                
                if not quality_result['passed']:
                    logger.warning("响应质量验证失败，尝试重新生成")
                    # 可以尝试重新生成或使用备用策略
                    self.generation_stats['quality_failed'] += 1
                else:
                    self.generation_stats['quality_passed'] += 1
            else:
                quality_result = {'overall_score': 0.8, 'passed': True}
            
            # 5. 生成引用
            citations = []
            if self.config.enable_citation_generation:
                citations = self._generate_citations(context_info['used_sources'])
            
            # 构建生成结果
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            result = GenerationResult(
                response=response_text,
                sources=citations,
                confidence=quality_result['overall_score'],
                metadata={
                    'task_type': task_type.value,
                    'processing_time': processing_time,
                    'context_length': context_info['context_length'],
                    'source_count': context_info['source_count'],
                    'quality_scores': quality_result,
                    'prompt_length': len(prompt)
                }
            )
            
            self.generation_stats['successful_generations'] += 1
            logger.info(f"响应生成完成: 耗时={processing_time:.2f}s, 置信度={quality_result['overall_score']:.2f}")
            
            return result
            
        except Exception as e:
            self.generation_stats['failed_generations'] += 1
            logger.error(f"响应生成失败: {str(e)}")
            
            # 返回错误结果
            return GenerationResult(
                response=f"抱歉，生成响应时出现错误：{str(e)}",
                sources=[],
                confidence=0.0,
                metadata={
                    'error': str(e),
                    'task_type': task_type.value,
                    'processing_time': (datetime.utcnow() - start_time).total_seconds()
                }
            )
    
    async def _call_llm(self, prompt: str, task_type: TaskType) -> str:
        """调用LLM生成响应"""
        try:
            # 构建消息
            messages = [QwenMessage(role='user', content=prompt)]

            # 调用AI模型
            response = await self.model_manager.chat_completion(
                messages=messages,
                temperature=self.config.temperature,
                max_tokens=self.config.max_response_tokens
            )

            if response.error:
                logger.warning(f"LLM调用失败，使用模拟响应: {response.error}")
                return await self._generate_mock_response(task_type)

            return response.content

        except Exception as e:
            logger.error(f"LLM调用异常: {e}")
            return await self._generate_mock_response(task_type)

    async def _generate_mock_response(self, task_type: TaskType) -> str:
        """生成模拟响应"""
        # 根据任务类型生成不同的模拟响应
        if task_type == TaskType.RISK_ASSESSMENT:
            return "基于提供的合同信息，我识别出以下主要风险：\n\n1. 法律风险（中等）：某些条款可能存在法律合规问题\n2. 商业风险（低）：商业条款总体合理\n3. 操作风险（中等）：执行过程中可能遇到协调问题\n4. 财务风险（高）：付款条款存在一定风险\n\n建议加强对高风险项目的关注和缓解措施。"
        elif task_type == TaskType.CONTRACT_ANALYSIS:
            return "合同分析结果：\n\n1. 基本信息：双方当事人明确，合同期限清晰\n2. 核心条款：权利义务分配基本合理\n3. 特殊条款：包含标准的争议解决条款\n4. 完整性：合同结构完整，条款覆盖全面\n5. 合规性：符合相关法律法规要求\n\n总体评价：这是一份结构完整、条款清晰的合同。"
        else:
            return "基于检索到的相关信息，我为您提供以下回答：\n\n这是一个模拟的生成响应，展示了RAG系统如何结合检索到的信息生成准确、相关的回答。在实际应用中，这里会调用真实的大语言模型来生成更加智能和准确的响应。"
    
    def _generate_citations(self, sources: List[RetrievalResult]) -> List[Dict[str, Any]]:
        """生成引用"""
        citations = []
        
        for i, source in enumerate(sources):
            citation = {
                'id': i + 1,
                'document_id': source.document_id,
                'source': source.metadata.get('source', f'文档{i+1}'),
                'relevance_score': source.score,
                'content_snippet': source.content[:150] + '...' if len(source.content) > 150 else source.content
            }
            citations.append(citation)
        
        return citations
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取生成统计信息"""
        total_requests = self.generation_stats['total_requests']
        
        return {
            **self.generation_stats,
            'success_rate': self.generation_stats['successful_generations'] / max(total_requests, 1),
            'quality_pass_rate': self.generation_stats['quality_passed'] / max(total_requests, 1)
        }
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            # 执行简单的生成测试
            test_results = []
            test_result = RetrievalResult(
                document_id="test_doc",
                content="这是一个测试文档内容",
                score=0.9,
                metadata={'source': 'test.pdf'}
            )
            
            result = await self.generate("测试查询", [test_result], TaskType.GENERAL_QA)
            return result.confidence > 0.0
            
        except Exception as e:
            logger.error(f"生成引擎健康检查失败: {str(e)}")
            return False
