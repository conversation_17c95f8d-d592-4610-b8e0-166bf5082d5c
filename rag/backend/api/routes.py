"""
RAG系统API路由
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent if Path(__file__).parent.name != "backend" else Path(__file__).parent
sys.path.insert(0, str(project_root))

import uuid
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, HTTPException, UploadFile, File, Depends, Query as QueryParam
from fastapi.responses import JSONResponse

from models import QueryRequest as ModelQueryRequest, QueryResponse as ModelQueryResponse
from .schemas import (
    QueryRequest, QueryResponse, DocumentResponse, HealthResponse,
    ErrorResponse, DocumentListResponse, StatsResponse,
    DocumentUploadRequest, KnowledgeBaseBuildRequest
)
from config import get_settings
from utils import get_logger

# 创建路由器
router = APIRouter()
logger = get_logger(__name__)
settings = get_settings()


# 健康检查
@router.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查接口"""
    try:
        # 获取RAG控制器实例
        from core.controller import RAGController
        rag_controller = RAGController()

        # 执行健康检查
        health_status = await rag_controller.health_check()

        # 转换为API响应格式
        return HealthResponse(
            status=health_status.get('status', 'unknown'),
            components=health_status.get('components', {})
        )

    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=503, detail="服务不可用")


# 系统信息
@router.get("/info")
async def get_system_info():
    """获取系统信息"""
    return {
        "name": "RAG System",
        "version": "1.0.0",
        "description": "检索增强生成系统",
        "api_version": "v1",
        "timestamp": datetime.utcnow().isoformat()
    }


# 文档管理
@router.post("/documents/upload", response_model=DocumentResponse)
async def upload_document(
    file: UploadFile = File(...),
    document_type: str = "other",
    tags: Optional[str] = None
):
    """上传文档"""
    try:
        # 验证文件类型
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        file_extension = file.filename.split('.')[-1].lower()
        if file_extension not in settings.upload_allowed_types_list:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件类型: {file_extension}"
            )
        
        # 验证文件大小
        if file.size and file.size > settings.upload_max_size:
            raise HTTPException(
                status_code=400,
                detail=f"文件大小超过限制: {settings.upload_max_size} bytes"
            )
        
        # 生成文档ID
        document_id = str(uuid.uuid4())
        
        # TODO: 实际的文件上传和处理逻辑
        # 1. 保存文件到存储
        # 2. 提取文档内容
        # 3. 文本处理和分块
        # 4. 向量化
        # 5. 存储到向量数据库
        
        # 解析标签
        tag_list = []
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
        
        logger.info(f"文档上传成功: {document_id}, 文件名: {file.filename}")
        
        return DocumentResponse(
            document_id=document_id,
            filename=file.filename,
            document_type=document_type,
            status="processing",
            created_at=datetime.utcnow(),
            metadata={
                "file_size": file.size,
                "file_type": file_extension,
                "tags": tag_list
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文档上传失败: {e}")
        raise HTTPException(status_code=500, detail="文档上传失败")


@router.get("/documents", response_model=DocumentListResponse)
async def list_documents(
    page: int = QueryParam(1, ge=1),
    page_size: int = QueryParam(10, ge=1, le=100),
    document_type: Optional[str] = None,
    status: Optional[str] = None
):
    """获取文档列表"""
    try:
        # TODO: 从数据库查询文档列表
        # 这里返回模拟数据
        documents = [
            DocumentResponse(
                document_id=str(uuid.uuid4()),
                filename=f"document_{i}.pdf",
                document_type="contract",
                status="processed",
                created_at=datetime.utcnow(),
                metadata={"file_size": 1024000}
            )
            for i in range(1, 6)
        ]
        
        total = len(documents)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        page_documents = documents[start_idx:end_idx]
        
        return DocumentListResponse.create(
            items=page_documents,
            total=total,
            page=page,
            page_size=page_size
        )
        
    except Exception as e:
        logger.error(f"获取文档列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取文档列表失败")


@router.delete("/documents/{document_id}")
async def delete_document(document_id: str):
    """删除文档"""
    try:
        # TODO: 实际的文档删除逻辑
        # 1. 从向量数据库删除
        # 2. 从文件系统删除
        # 3. 从数据库删除记录
        
        logger.info(f"文档删除成功: {document_id}")
        
        return {"message": "文档删除成功", "document_id": document_id}
        
    except Exception as e:
        logger.error(f"文档删除失败: {e}")
        raise HTTPException(status_code=500, detail="文档删除失败")


# RAG查询
@router.post("/rag/query", response_model=QueryResponse)
async def rag_query(request: QueryRequest):
    """RAG查询接口"""
    try:
        logger.info(f"收到RAG查询请求: {request.query[:100]}...")

        # 获取RAG控制器实例
        from core.controller import RAGController
        rag_controller = RAGController()

        # 处理查询
        response = await rag_controller.query(request)

        return response
        
    except Exception as e:
        logger.error(f"RAG查询处理失败: {e}")
        raise HTTPException(status_code=500, detail="查询处理失败")


# 知识库管理
@router.post("/knowledge/upload")
async def upload_document(file: UploadFile = File(...)):
    """上传文档到知识库"""
    try:
        logger.info(f"开始上传文档: {file.filename}")

        # 获取RAG控制器实例
        from core.controller import RAGController
        rag_controller = RAGController()

        # 保存上传的文件
        import tempfile
        import os

        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # 添加文档到知识库
            success = await rag_controller.add_document(
                temp_file_path,
                metadata={'original_filename': file.filename}
            )

            if success:
                return {
                    "success": True,
                    "filename": file.filename,
                    "message": "文档上传并处理成功"
                }
            else:
                raise HTTPException(status_code=500, detail="文档处理失败")

        finally:
            # 清理临时文件
            os.unlink(temp_file_path)

    except Exception as e:
        logger.error(f"文档上传失败: {e}")
        raise HTTPException(status_code=500, detail=f"文档上传失败: {str(e)}")


@router.post("/knowledge/build")
async def build_knowledge_base(request: KnowledgeBaseBuildRequest):
    """构建知识库"""
    try:
        logger.info(f"开始构建知识库: {request.source_path}")
        
        # TODO: 实际的知识库构建逻辑
        # 1. 扫描源目录
        # 2. 批量处理文档
        # 3. 构建向量索引
        # 4. 更新知识库状态
        
        return {
            "message": "知识库构建任务已启动",
            "task_id": str(uuid.uuid4()),
            "source_path": request.source_path,
            "rebuild": request.rebuild
        }
        
    except Exception as e:
        logger.error(f"知识库构建失败: {e}")
        raise HTTPException(status_code=500, detail="知识库构建失败")


@router.get("/knowledge/status")
async def get_knowledge_base_status():
    """获取知识库状态"""
    try:
        # 获取RAG控制器实例
        from core.controller import RAGController
        rag_controller = RAGController()

        # 获取实际的知识库统计信息
        metrics = await rag_controller.get_metrics()

        return {
            "status": "ready",
            "knowledge_base_size": metrics.get('knowledge_base_size', 0),
            "cache_size": metrics.get('cache_size', 0),
            "total_queries": metrics.get('total_queries', 0),
            "cache_hit_rate": metrics.get('cache_hit_rate', 0.0),
            "error_rate": metrics.get('error_rate', 0.0),
            "avg_latency": metrics.get('avg_latency', 0.0),
            "last_updated": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"获取知识库状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取知识库状态失败")





# 统计信息
@router.get("/stats", response_model=StatsResponse)
async def get_stats():
    """获取系统统计信息"""
    try:
        # TODO: 查询实际的统计数据
        return StatsResponse(
            total_documents=150,
            total_queries=1250,
            avg_processing_time=1.2,
            success_rate=0.95
        )
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取统计信息失败")
