"""
文档相关数据模型
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum


class DocumentType(str, Enum):
    """文档类型枚举"""
    CONTRACT = "contract"
    LEGAL_DOC = "legal_doc"
    REGULATION = "regulation"
    CASE_STUDY = "case_study"
    TEMPLATE = "template"
    OTHER = "other"


class DocumentStatus(str, Enum):
    """文档状态枚举"""
    UPLOADING = "uploading"
    PROCESSING = "processing"
    PROCESSED = "processed"
    FAILED = "failed"
    DELETED = "deleted"


class DocumentMetadata(BaseModel):
    """文档元数据"""
    title: Optional[str] = None
    author: Optional[str] = None
    created_date: Optional[datetime] = None
    modified_date: Optional[datetime] = None
    file_size: Optional[int] = None
    page_count: Optional[int] = None
    language: Optional[str] = "zh-CN"
    tags: List[str] = Field(default_factory=list)
    category: Optional[str] = None
    source: Optional[str] = None
    version: Optional[str] = None
    custom_fields: Dict[str, Any] = Field(default_factory=dict)


class DocumentChunk(BaseModel):
    """文档分块"""
    chunk_id: str
    document_id: str
    content: str
    chunk_index: int
    start_pos: int
    end_pos: int
    word_count: int
    char_count: int
    metadata: Dict[str, Any] = Field(default_factory=dict)
    embedding: Optional[List[float]] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class Document(BaseModel):
    """文档模型"""
    document_id: str
    filename: str
    original_filename: str
    file_path: str
    file_type: str
    document_type: DocumentType = DocumentType.OTHER
    status: DocumentStatus = DocumentStatus.UPLOADING
    content: Optional[str] = None
    metadata: DocumentMetadata = Field(default_factory=DocumentMetadata)
    chunks: List[DocumentChunk] = Field(default_factory=list)
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    processed_at: Optional[datetime] = None
    
    # 处理信息
    processing_info: Dict[str, Any] = Field(default_factory=dict)
    error_message: Optional[str] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
    
    def update_status(self, status: DocumentStatus, error_message: Optional[str] = None):
        """更新文档状态"""
        self.status = status
        self.updated_at = datetime.utcnow()
        if error_message:
            self.error_message = error_message
        if status == DocumentStatus.PROCESSED:
            self.processed_at = datetime.utcnow()
    
    def add_chunk(self, chunk: DocumentChunk):
        """添加文档分块"""
        self.chunks.append(chunk)
        self.updated_at = datetime.utcnow()
    
    def get_chunk_by_id(self, chunk_id: str) -> Optional[DocumentChunk]:
        """根据ID获取分块"""
        for chunk in self.chunks:
            if chunk.chunk_id == chunk_id:
                return chunk
        return None
    
    @property
    def total_chunks(self) -> int:
        """总分块数"""
        return len(self.chunks)
    
    @property
    def total_words(self) -> int:
        """总词数"""
        return sum(chunk.word_count for chunk in self.chunks)
    
    @property
    def is_processed(self) -> bool:
        """是否已处理完成"""
        return self.status == DocumentStatus.PROCESSED
    
    @property
    def has_embeddings(self) -> bool:
        """是否已生成向量"""
        return any(chunk.embedding is not None for chunk in self.chunks)
