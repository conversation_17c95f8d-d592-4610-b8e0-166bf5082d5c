"""
响应相关数据模型
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


class Citation(BaseModel):
    """引用信息"""
    source_id: str
    source_title: str
    source_type: str
    page_number: Optional[int] = None
    chunk_id: Optional[str] = None
    excerpt: str
    relevance_score: float
    url: Optional[str] = None


class RetrievalResult(BaseModel):
    """检索结果"""
    document_id: str
    chunk_id: str
    content: str
    score: float
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    # 文档信息
    document_title: Optional[str] = None
    document_type: Optional[str] = None
    page_number: Optional[int] = None
    
    # 检索信息
    retrieval_method: Optional[str] = None  # vector, keyword, hybrid
    rerank_score: Optional[float] = None


class GenerationResult(BaseModel):
    """生成结果"""
    content: str
    model: str
    tokens_used: int
    processing_time: float
    
    # 质量指标
    confidence_score: Optional[float] = None
    hallucination_score: Optional[float] = None
    factual_consistency_score: Optional[float] = None
    
    # 生成参数
    temperature: float
    max_tokens: Optional[int] = None
    
    # 时间戳
    generated_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class RAGResponse(BaseModel):
    """RAG完整响应"""
    query_id: str
    query: str
    answer: str
    
    # 检索结果
    retrieval_results: List[RetrievalResult] = Field(default_factory=list)
    
    # 生成结果
    generation_result: Optional[GenerationResult] = None
    
    # 引用信息
    citations: List[Citation] = Field(default_factory=list)
    
    # 元信息
    total_processing_time: float
    retrieval_time: float
    generation_time: float
    
    # 质量指标
    overall_confidence: Optional[float] = None
    retrieval_quality: Optional[float] = None
    generation_quality: Optional[float] = None
    
    # 统计信息
    total_sources: int
    unique_documents: int
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
    
    @property
    def has_citations(self) -> bool:
        """是否包含引用"""
        return len(self.citations) > 0
    
    @property
    def average_retrieval_score(self) -> float:
        """平均检索分数"""
        if not self.retrieval_results:
            return 0.0
        return sum(result.score for result in self.retrieval_results) / len(self.retrieval_results)
    
    def add_citation(self, citation: Citation):
        """添加引用"""
        self.citations.append(citation)
    
    def get_top_sources(self, n: int = 3) -> List[RetrievalResult]:
        """获取前N个最相关的源"""
        return sorted(self.retrieval_results, key=lambda x: x.score, reverse=True)[:n]


class ErrorResponse(BaseModel):
    """错误响应"""
    error_code: str
    error_message: str
    error_type: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: str
    components: Dict[str, Dict[str, Any]] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class MetricsResponse(BaseModel):
    """指标响应"""
    metrics: Dict[str, Any] = Field(default_factory=dict)
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
