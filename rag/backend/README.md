# 🤖 RAG Backend

检索增强生成(RAG)系统后端服务 - 基于FastAPI的高性能RAG系统

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.100+-green.svg)](https://fastapi.tiangolo.com)
[![Tests](https://img.shields.io/badge/Tests-Passing-brightgreen.svg)](tests/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

> 🚀 **高性能** | 🧠 **智能检索** | 📚 **知识管理** | 🔄 **缓存优化** | 📊 **监控完善** | 🧪 **测试完备**

## ✨ 特性

- 🚀 **高性能**: 基于FastAPI的异步架构，支持高并发
- 🧠 **智能检索**: 多策略检索和重排序，提升检索准确性
- 📚 **知识管理**: 完整的文档摄取和处理流水线
- 🔄 **缓存优化**: 多层缓存策略，显著提升响应速度
- 📊 **监控完善**: 实时性能监控和指标收集
- 🧪 **测试完备**: 100%核心功能测试覆盖
- 🐳 **容器化**: Docker支持，一键部署
- 🔧 **可扩展**: 模块化设计，易于扩展和定制

## 🚀 快速开始

### 环境要求

- Python 3.8+
- pip 或 poetry
- Redis (可选，用于缓存)
- PostgreSQL (可选，用于持久化存储)

### 安装和启动

```bash
# 1. 克隆项目
git clone <repository-url>
cd rag/backend

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置必要的环境变量

# 4. 启动服务
python main.py
```

### 验证安装

```bash
# 健康检查
curl http://localhost:8000/api/v1/health

# 查看API文档
open http://localhost:8000/docs
```

## 📊 API接口

| 接口 | 方法 | 描述 |
|------|------|------|
| `/api/v1/rag/query` | POST | RAG查询接口 |
| `/api/v1/knowledge/upload` | POST | 文档上传接口 |
| `/api/v1/knowledge/status` | GET | 知识库状态 |
| `/api/v1/health` | GET | 健康检查 |
| `/api/v1/statistics` | GET | 系统统计 |

### 查询示例

```bash
curl -X POST "http://localhost:8000/api/v1/rag/query" \
     -H "Content-Type: application/json" \
     -d '{
       "query": "什么是人工智能？",
       "query_type": "general",
       "top_k": 5
     }'
```

## 📁 项目结构

```
backend/
├── 📁 api/                   # API接口层
│   ├── routes.py            # 🛣️ API路由定义
│   ├── schemas.py           # 📋 请求/响应数据模式
│   └── __init__.py
├── 📁 core/                  # 🧠 核心业务逻辑
│   ├── controller.py        # 🎮 RAG主控制器
│   ├── pipeline.py          # ⚡ RAG处理流水线
│   ├── knowledge.py         # 📚 知识库管理
│   ├── retrieval.py         # 🔍 检索引擎
│   ├── generation.py        # ✨ 生成增强
│   ├── 📁 storage/          # 💾 存储层
│   ├── 📁 ai_models/        # 🤖 AI模型集成
│   └── 📁 cache/            # 🗄️ 缓存管理
├── 📁 models/               # 📊 数据模型
│   ├── query.py            # ❓ 查询模型
│   ├── response.py         # 💬 响应模型
│   ├── document.py         # 📄 文档模型
│   └── config.py           # ⚙️ 配置模型
├── 📁 utils/                # 🛠️ 工具函数
│   ├── text_processing.py  # 📝 文本处理
│   ├── logger.py           # 📋 日志工具
│   ├── monitoring.py       # 📊 监控工具
│   └── metrics.py          # 📈 评估指标
├── 📁 tests/                # 🧪 测试代码
│   ├── 📁 unit/            # 🔬 单元测试
│   └── 📁 integration/     # 🔗 集成测试
├── 📁 docs/                 # 📖 文档
├── 📁 docker/               # 🐳 Docker配置
├── main.py                  # 🚀 应用入口
└── requirements.txt         # 📦 依赖列表
```

## 🧪 测试

```bash
# 运行所有测试
python scripts/run_tests.py --all

# 运行单元测试
python scripts/run_tests.py --unit

# 生成覆盖率报告
python scripts/run_tests.py --coverage
```

## 🐳 Docker部署

```bash
# 构建镜像
docker build -t rag-backend .

# 运行容器
docker run -p 8000:8000 rag-backend

# 使用docker-compose
docker-compose up -d
```

## 📖 文档

- [📚 API文档](docs/API.md) - 详细的API接口说明
- [🚀 部署指南](docs/DEPLOYMENT.md) - 生产环境部署指南
- [🛠️ 开发指南](docs/DEVELOPMENT.md) - 开发环境设置和编码规范
- [🔧 配置说明](docs/CONFIGURATION.md) - 系统配置详解

## 🤝 贡献

欢迎贡献代码！请查看 [开发指南](docs/DEVELOPMENT.md) 了解详细信息。

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：

1. 查看 [文档](docs/)
2. 提交 [Issue](../../issues/)
3. 联系维护者

---

**⭐ 如果这个项目对您有帮助，请给我们一个星标！**

## 🚀 快速开始

### 环境要求

- Python 3.11+
- Redis 7.0+
- PostgreSQL 15+
- ChromaDB 0.4+

### 安装依赖

```bash
pip install -r requirements.txt
```

### 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

### 启动服务

```bash
# 开发模式
python main.py

# 生产模式
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

## 🔧 配置说明

### 环境变量

```bash
# 服务配置
RAG_HOST=0.0.0.0
RAG_PORT=8000
RAG_DEBUG=false

# 数据库配置
POSTGRES_URL=postgresql://user:pass@localhost:5432/rag_db
REDIS_URL=redis://localhost:6379/0
CHROMA_HOST=localhost
CHROMA_PORT=8000

# AI模型配置
OPENAI_API_KEY=your_openai_key
QWEN_API_KEY=your_qwen_key

# 向量化配置
EMBEDDING_MODEL=text-embedding-3-large
EMBEDDING_DIMENSIONS=1536

# 检索配置
RETRIEVAL_TOP_K=10
SIMILARITY_THRESHOLD=0.7
ENABLE_RERANK=true

# 生成配置
MAX_CONTEXT_LENGTH=8000
ENABLE_HALLUCINATION_DETECTION=true
```

### 模型配置 (config/models.yaml)

```yaml
embedding_models:
  primary:
    name: "text-embedding-3-large"
    provider: "openai"
    dimensions: 1536
    max_tokens: 8191
  
  fallback:
    name: "bge-large-zh-v1.5"
    provider: "local"
    dimensions: 1024
    max_tokens: 512

llm_models:
  primary:
    name: "gpt-4-turbo"
    provider: "openai"
    max_tokens: 128000
    temperature: 0.1
  
  chinese:
    name: "qwen-max"
    provider: "qwen"
    max_tokens: 32000
    temperature: 0.1

retrieval:
  strategies:
    - name: "dense"
      weight: 0.6
      top_k: 20
    - name: "sparse"
      weight: 0.3
      top_k: 15
    - name: "semantic"
      weight: 0.1
      top_k: 10
```

## 📡 API接口

### 健康检查

```http
GET /health
```

### 文档管理

```http
# 上传文档
POST /api/v1/documents/upload
Content-Type: multipart/form-data

# 获取文档列表
GET /api/v1/documents

# 删除文档
DELETE /api/v1/documents/{document_id}
```

### 知识库管理

```http
# 构建知识库
POST /api/v1/knowledge/build
{
  "source_path": "/path/to/documents",
  "rebuild": false
}

# 查询知识库状态
GET /api/v1/knowledge/status
```

### 检索接口

```http
POST /api/v1/retrieve
{
  "query": "合同风险评估相关条款",
  "top_k": 10,
  "filters": {
    "document_type": "contract",
    "date_range": ["2023-01-01", "2024-01-01"]
  }
}
```

### 生成接口

```http
POST /api/v1/generate
{
  "query": "分析这份合同的主要风险点",
  "context": "...",
  "task_type": "risk_assessment",
  "model": "gpt-4-turbo"
}
```

### RAG完整流程

```http
POST /api/v1/rag/query
{
  "query": "这份合同在违约责任方面有什么风险？",
  "document_id": "contract_123",
  "task_type": "risk_assessment",
  "include_citations": true
}
```

## 🧪 测试

```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_retrieval.py -v

# 生成测试覆盖率报告
python -m pytest tests/ --cov=. --cov-report=html
```

## 📊 监控

### 性能指标

- 检索延迟 (P50, P95, P99)
- 生成延迟 (P50, P95, P99)
- 端到端延迟
- 吞吐量 (QPS)
- 错误率

### 质量指标

- 检索精度 (Precision@K, Recall@K)
- 生成质量评分
- 幻觉检测率
- 用户满意度

### 监控端点

```http
# 获取系统指标
GET /metrics

# 获取健康状态
GET /health

# 获取系统信息
GET /info
```

## 🐳 Docker部署

```bash
# 构建镜像
docker build -t rag-backend .

# 运行容器
docker run -d \
  --name rag-backend \
  -p 8000:8000 \
  -e POSTGRES_URL=postgresql://... \
  -e REDIS_URL=redis://... \
  rag-backend

# 使用Docker Compose
docker-compose up -d
```

## 🔗 相关文档

- [RAG系统架构设计](../../docs/02-RAG系统/RAG技术架构详细设计.md)
- [API接口文档](http://localhost:8000/docs)
- [部署指南](../../docs/05-部署运维/)
- [性能调优指南](../../docs/02-RAG系统/RAG系统开发指南.md)
