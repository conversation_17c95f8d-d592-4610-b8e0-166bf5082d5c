"""
pytest配置文件
定义测试夹具和共享配置
"""

import asyncio
import pytest
import pytest_asyncio
import tempfile
import shutil
import sys
import os
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock

# 设置测试环境
os.environ['TESTING'] = 'true'

# 确保项目根目录在Python路径中
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['PYTHONPATH'] = str(project_root)

# 导入测试配置
from tests.test_config import setup_test_environment
setup_test_environment()

# 导入模块，使用try-except处理导入错误
try:
    from models.query import QueryRequest
    from models.response import RAGResponse, RetrievalResult
    from models.config import RAGConfig
    MODELS_AVAILABLE = True
except ImportError as e:
    print(f"模型导入错误: {e}")
    MODELS_AVAILABLE = False

try:
    from core.controller import RAGController
    from core.knowledge import KnowledgeManager
    CORE_AVAILABLE = True
except ImportError as e:
    print(f"核心模块导入错误: {e}")
    CORE_AVAILABLE = False

try:
    from core.storage.database import DatabaseManager
    from core.storage.vector_store import VectorStoreManager
    from core.storage.cache_store import CacheManager
    STORAGE_AVAILABLE = True
except ImportError as e:
    print(f"存储模块导入错误: {e}")
    STORAGE_AVAILABLE = False

try:
    from core.ai_models.model_manager import ModelManager
    AI_MODELS_AVAILABLE = True
except ImportError as e:
    print(f"AI模型导入错误: {e}")
    AI_MODELS_AVAILABLE = False

# 创建模拟类
if not MODELS_AVAILABLE:
    class QueryRequest:
        def __init__(self, query="", query_type="general_query", top_k=5, filters=None):
            self.query = query
            self.query_type = query_type
            self.top_k = top_k
            self.filters = filters

    class RAGResponse:
        def __init__(self, response="", sources=None, confidence=0.8, context="", metadata=None):
            self.response = response
            self.sources = sources or []
            self.confidence = confidence
            self.context = context
            self.metadata = metadata or {}
            self.success = True
            self.query_id = "mock_query_id"
            self.processing_time = 1.0
            self.from_cache = False

    class RetrievalResult:
        def __init__(self, document_id="", content="", score=0.8, metadata=None):
            self.document_id = document_id
            self.content = content
            self.score = score
            self.metadata = metadata or {}

    class RAGConfig:
        def __init__(self, **kwargs):
            self.enable_cache = kwargs.get('enable_cache', True)
            self.cache_ttl = kwargs.get('cache_ttl', 300)
            self.max_context_length = kwargs.get('max_context_length', 2000)
            self.enable_monitoring = kwargs.get('enable_monitoring', False)
            self.debug_mode = kwargs.get('debug_mode', True)

if not CORE_AVAILABLE:
    class RAGController:
        def __init__(self, config=None):
            self.config = config or RAGConfig()
            self.initialized = False
            self.metrics = {'total_queries': 0, 'cache_hits': 0, 'cache_misses': 0, 'error_count': 0}

        async def initialize(self):
            self.initialized = True
            return True

        async def shutdown(self):
            self.initialized = False

        async def query(self, request):
            self.metrics['total_queries'] += 1
            return RAGResponse(
                response="模拟响应",
                sources=[],
                confidence=0.8
            )

        async def health_check(self):
            return {'status': 'healthy', 'components': {}, 'timestamp': '2024-01-01T00:00:00Z'}

    class KnowledgeManager:
        def __init__(self):
            self.initialized = False

        async def initialize(self):
            self.initialized = True
            return True

        async def shutdown(self):
            self.initialized = False


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir():
    """创建临时目录"""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


@pytest.fixture
def test_config():
    """测试配置"""
    return RAGConfig(
        enable_cache=True,
        cache_ttl=300,
        max_context_length=2000,
        enable_monitoring=False,
        debug_mode=True
    )


@pytest_asyncio.fixture
async def mock_database_manager():
    """模拟数据库管理器"""
    manager = MagicMock()
    manager.connected = True

    # 模拟异步方法
    manager.save_document = AsyncMock(return_value=True)
    manager.get_document = AsyncMock(return_value=None)
    manager.delete_document = AsyncMock(return_value=True)
    manager.save_document_chunks = AsyncMock(return_value=True)
    manager.update_document_status = AsyncMock(return_value=True)
    manager.save_query_history = AsyncMock(return_value=True)
    manager.get_statistics = AsyncMock(return_value={
        'total_documents': 10,
        'total_chunks': 100,
        'total_queries': 50
    })
    manager.health_check = AsyncMock(return_value=True)
    manager.connect = AsyncMock(return_value=True)
    manager.disconnect = AsyncMock(return_value=True)

    return manager


@pytest_asyncio.fixture
async def mock_vector_store():
    """模拟向量存储"""
    store = MagicMock()
    store.connected = True

    # 模拟异步方法
    store.add_documents = AsyncMock(return_value=True)
    store.search_similar = AsyncMock(return_value=[
        {
            'id': 'test_doc_1',
            'content': '测试文档内容1',
            'metadata': {'source': 'test1.pdf'},
            'distance': 0.1,
            'score': 0.9
        },
        {
            'id': 'test_doc_2',
            'content': '测试文档内容2',
            'metadata': {'source': 'test2.pdf'},
            'distance': 0.2,
            'score': 0.8
        }
    ])
    store.delete_documents = AsyncMock(return_value=True)
    store.get_collection_info = AsyncMock(return_value={
        'name': 'test_collection',
        'count': 100
    })
    store.health_check = AsyncMock(return_value=True)
    store.connect = AsyncMock(return_value=True)
    store.disconnect = AsyncMock(return_value=True)

    return store


@pytest_asyncio.fixture
async def mock_cache_manager():
    """模拟缓存管理器"""
    cache = MagicMock()
    cache.connected = True

    # 模拟异步方法
    cache.set = AsyncMock(return_value=True)
    cache.get = AsyncMock(return_value=None)
    cache.delete = AsyncMock(return_value=True)
    cache.exists = AsyncMock(return_value=False)
    cache.clear_pattern = AsyncMock(return_value=0)
    cache.get_statistics = AsyncMock(return_value={
        'total_keys': 0,
        'cache_type': 'memory'
    })
    cache.health_check = AsyncMock(return_value=True)
    cache.connect = AsyncMock(return_value=True)
    cache.disconnect = AsyncMock(return_value=True)

    return cache


@pytest_asyncio.fixture
async def mock_model_manager():
    """模拟AI模型管理器"""
    manager = MagicMock()

    # 模拟异步方法
    manager.initialize = AsyncMock(return_value=True)
    manager.chat_completion = AsyncMock(return_value=MagicMock(
        content="这是一个模拟的AI响应",
        model="test-model",
        usage={'total_tokens': 100},
        finish_reason='stop',
        latency=0.5,
        error=None
    ))
    manager.create_embeddings = AsyncMock(return_value=MagicMock(
        embeddings=[[0.1] * 768, [0.2] * 768],
        model="test-embedding",
        usage={'total_tokens': 50},
        latency=0.3,
        error=None
    ))
    manager.health_check = AsyncMock(return_value={
        'status': 'healthy',
        'models': {'qwen': 'ok', 'embedding': 'ok'}
    })
    manager.shutdown = AsyncMock()

    return manager


@pytest_asyncio.fixture
async def knowledge_manager(mock_database_manager, mock_vector_store, mock_model_manager):
    """知识库管理器夹具"""
    manager = MagicMock()
    manager.db_manager = mock_database_manager
    manager.vector_store = mock_vector_store
    manager.model_manager = mock_model_manager
    manager.initialized = True

    # 模拟异步方法
    manager.initialize = AsyncMock(return_value=True)
    manager.shutdown = AsyncMock(return_value=True)
    manager.add_document = AsyncMock(return_value=True)
    manager.remove_document = AsyncMock(return_value=True)
    manager.search_documents = AsyncMock(return_value=[])
    manager.get_statistics = AsyncMock(return_value={'document_count': 10})
    manager.health_check = AsyncMock(return_value=True)

    return manager


@pytest_asyncio.fixture
async def rag_controller(test_config, knowledge_manager, mock_cache_manager):
    """RAG控制器夹具"""
    controller = MagicMock()
    controller.config = test_config
    controller.knowledge_manager = knowledge_manager
    controller.cache_manager = mock_cache_manager
    controller.initialized = True
    controller.metrics = {'total_queries': 0, 'cache_hits': 0, 'cache_misses': 0, 'error_count': 0}

    # 模拟异步方法
    controller.initialize = AsyncMock(return_value=True)
    controller.shutdown = AsyncMock(return_value=True)
    controller.query = AsyncMock(return_value=MagicMock(
        success=True,
        response="模拟响应",
        confidence=0.8,
        sources=[],
        query_id="test_query_id",
        processing_time=1.0,
        from_cache=False
    ))
    controller.add_document = AsyncMock(return_value=True)
    controller.get_metrics = AsyncMock(return_value={'total_queries': 0})
    controller.health_check = AsyncMock(return_value={'status': 'healthy'})

    return controller


@pytest.fixture
def sample_query_request():
    """示例查询请求"""
    try:
        from models.query import QueryRequest, QueryType
        return QueryRequest(
            query="什么是人工智能？",
            query_type=QueryType.GENERAL,
            top_k=5,
            filters=None
        )
    except ImportError:
        # 如果导入失败，返回简单对象
        from types import SimpleNamespace
        return SimpleNamespace(
            query="什么是人工智能？",
            query_type="general",
            top_k=5,
            filters=None
        )


@pytest.fixture
def sample_rag_response():
    """示例RAG响应"""
    return RAGResponse(
        response="人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
        sources=[
            {
                'id': 1,
                'title': 'AI概述',
                'content': '人工智能基础知识...',
                'score': 0.9,
                'metadata': {'source': 'ai_basics.pdf'}
            }
        ],
        confidence=0.85,
        context="相关的上下文信息",
        metadata={
            'processing_time': 1.5,
            'model_used': 'test-model',
            'retrieval_count': 5
        }
    )


@pytest.fixture
def sample_retrieval_results():
    """示例检索结果"""
    return [
        RetrievalResult(
            document_id="doc_1",
            content="人工智能是一门综合性学科...",
            score=0.9,
            metadata={'source': 'ai_intro.pdf', 'page': 1}
        ),
        RetrievalResult(
            document_id="doc_2",
            content="机器学习是AI的重要分支...",
            score=0.8,
            metadata={'source': 'ml_guide.pdf', 'page': 3}
        ),
        RetrievalResult(
            document_id="doc_3",
            content="深度学习技术的发展...",
            score=0.75,
            metadata={'source': 'dl_trends.pdf', 'page': 5}
        )
    ]


@pytest.fixture
def sample_document_content():
    """示例文档内容"""
    return {
        'filename': 'test_document.pdf',
        'content': '''
        人工智能技术概述
        
        人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，
        它致力于创建能够执行通常需要人类智能的任务的系统。
        
        主要应用领域包括：
        1. 自然语言处理
        2. 计算机视觉
        3. 机器学习
        4. 专家系统
        
        AI技术的发展历程可以分为几个重要阶段：
        - 1950年代：AI概念的提出
        - 1980年代：专家系统的兴起
        - 2010年代：深度学习的突破
        - 2020年代：大语言模型的发展
        ''',
        'metadata': {
            'file_size': 1024,
            'file_type': 'pdf',
            'created_at': '2024-01-01T00:00:00Z',
            'author': 'Test Author'
        }
    }


@pytest.fixture(autouse=True)
async def cleanup_test_data():
    """自动清理测试数据"""
    yield
    # 测试后清理逻辑
    # 这里可以添加清理临时文件、数据库等的代码


# 测试标记
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.slow = pytest.mark.slow
pytest.mark.asyncio = pytest.mark.asyncio
