"""
RAG控制器单元测试
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent if Path(__file__).parent.name != "backend" else Path(__file__).parent
sys.path.insert(0, str(project_root))

import pytest
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock

from core.controller import RAGController, RAGConfig
from models.query import QueryRequest
from models.response import RAGResponse


@pytest.mark.unit
@pytest.mark.asyncio
class TestRAGController:
    """RAG控制器测试类"""
    
    async def test_initialization(self, rag_controller):
        """测试初始化"""
        assert rag_controller is not None
        assert rag_controller.config is not None
        assert rag_controller.knowledge_manager is not None
        assert rag_controller.cache_manager is not None
        assert rag_controller.initialized is True
    
    async def test_query_without_cache(self, rag_controller, sample_query_request):
        """测试无缓存查询"""
        # 禁用缓存
        rag_controller.config.enable_cache = False
        
        # 模拟流水线处理
        mock_response = RAGResponse(
            response="这是一个测试响应",
            sources=[],
            confidence=0.8,
            context="测试上下文",
            metadata={}
        )
        rag_controller.pipeline.process = AsyncMock(return_value=mock_response)
        
        # 执行查询
        response = await rag_controller.query(sample_query_request)
        
        assert response is not None
        assert response.success is True
        assert response.response == mock_response.response
        assert response.confidence == mock_response.confidence
        assert response.from_cache is False
        
        # 验证统计更新
        assert rag_controller.metrics['total_queries'] == 1
        assert rag_controller.metrics['cache_misses'] == 1
    
    async def test_query_with_cache_miss(self, rag_controller, sample_query_request):
        """测试缓存未命中的查询"""
        # 启用缓存
        rag_controller.config.enable_cache = True
        
        # 模拟缓存未命中
        rag_controller.query_cache.get_cached_response = AsyncMock(return_value=None)
        rag_controller.query_cache.cache_response = AsyncMock(return_value=True)
        
        # 模拟流水线处理
        mock_response = RAGResponse(
            response="这是一个测试响应",
            sources=[],
            confidence=0.8,
            context="测试上下文",
            metadata={}
        )
        rag_controller.pipeline.process = AsyncMock(return_value=mock_response)
        
        # 执行查询
        response = await rag_controller.query(sample_query_request)
        
        assert response is not None
        assert response.success is True
        assert response.from_cache is False
        
        # 验证缓存调用
        rag_controller.query_cache.get_cached_response.assert_called_once()
        rag_controller.query_cache.cache_response.assert_called_once()
        
        # 验证统计更新
        assert rag_controller.metrics['cache_misses'] == 1
    
    async def test_query_with_cache_hit(self, rag_controller, sample_query_request, sample_rag_response):
        """测试缓存命中的查询"""
        # 启用缓存
        rag_controller.config.enable_cache = True
        
        # 模拟缓存命中
        rag_controller.query_cache.get_cached_response = AsyncMock(return_value=sample_rag_response)
        
        # 执行查询
        response = await rag_controller.query(sample_query_request)
        
        assert response is not None
        assert response.success is True
        assert response.from_cache is True
        assert response.response == sample_rag_response.response
        
        # 验证缓存调用
        rag_controller.query_cache.get_cached_response.assert_called_once()
        
        # 验证统计更新
        assert rag_controller.metrics['cache_hits'] == 1
    
    async def test_query_error_handling(self, rag_controller, sample_query_request):
        """测试查询错误处理"""
        # 模拟流水线处理异常
        rag_controller.pipeline.process = AsyncMock(side_effect=Exception("测试异常"))
        
        # 执行查询
        response = await rag_controller.query(sample_query_request)
        
        assert response is not None
        assert response.success is False
        assert "查询处理失败" in response.response
        
        # 验证错误统计更新
        assert rag_controller.metrics['error_count'] == 1
    
    async def test_add_document_success(self, rag_controller, temp_dir):
        """测试成功添加文档"""
        # 创建测试文件
        test_file = temp_dir / "test.txt"
        test_file.write_text("测试文档内容")
        
        # 模拟知识库管理器
        rag_controller.knowledge_manager.add_document = AsyncMock(return_value=True)
        
        # 添加文档
        success = await rag_controller.add_document(str(test_file))
        
        assert success is True
        rag_controller.knowledge_manager.add_document.assert_called_once()
        
        # 验证缓存清理
        rag_controller.query_cache.invalidate_cache.assert_called_once()
    
    async def test_add_document_failure(self, rag_controller):
        """测试添加文档失败"""
        # 模拟知识库管理器失败
        rag_controller.knowledge_manager.add_document = AsyncMock(return_value=False)
        
        # 添加文档
        success = await rag_controller.add_document("/nonexistent/file.txt")
        
        assert success is False
    
    async def test_get_metrics(self, rag_controller):
        """测试获取性能指标"""
        # 设置一些测试数据
        rag_controller.metrics['total_queries'] = 100
        rag_controller.metrics['cache_hits'] = 30
        rag_controller.metrics['error_count'] = 5
        
        # 模拟组件统计
        rag_controller.cache_manager.get_statistics = AsyncMock(return_value={
            'total_keys': 50,
            'cache_type': 'memory'
        })
        rag_controller.query_cache.get_cache_statistics = AsyncMock(return_value={
            'hit_rate': 0.3,
            'hot_queries_count': 10
        })
        rag_controller.knowledge_manager.get_statistics = AsyncMock(return_value={
            'document_count': 200
        })
        
        # 获取指标
        metrics = await rag_controller.get_metrics()
        
        assert isinstance(metrics, dict)
        assert metrics['total_queries'] == 100
        assert metrics['cache_hit_rate'] == 0.3
        assert metrics['error_rate'] == 0.05
        assert 'knowledge_base_size' in metrics
        assert 'cache_size' in metrics
        assert 'query_cache' in metrics
    
    async def test_health_check(self, rag_controller):
        """测试健康检查"""
        # 模拟各组件健康状态
        rag_controller.knowledge_manager.health_check = AsyncMock(return_value=True)
        rag_controller.cache_manager.health_check = AsyncMock(return_value=True)
        rag_controller.generation_engine.health_check = AsyncMock(return_value=True)
        
        # 执行健康检查
        health_status = await rag_controller.health_check()
        
        assert isinstance(health_status, dict)
        assert health_status['status'] == 'healthy'
        assert 'components' in health_status
        assert 'timestamp' in health_status
        
        # 验证各组件健康检查调用
        rag_controller.knowledge_manager.health_check.assert_called_once()
        rag_controller.cache_manager.health_check.assert_called_once()
    
    async def test_health_check_with_failures(self, rag_controller):
        """测试部分组件失败的健康检查"""
        # 模拟部分组件失败
        rag_controller.knowledge_manager.health_check = AsyncMock(return_value=False)
        rag_controller.cache_manager.health_check = AsyncMock(return_value=True)
        rag_controller.generation_engine.health_check = AsyncMock(return_value=True)
        
        # 执行健康检查
        health_status = await rag_controller.health_check()
        
        assert health_status['status'] == 'degraded'
        assert health_status['components']['knowledge_manager'] == 'error'
        assert health_status['components']['cache_manager'] == 'ok'
    
    async def test_generate_cache_key(self, rag_controller):
        """测试缓存键生成"""
        query1 = "测试查询"
        query2 = "测试查询"
        query3 = "不同查询"
        
        key1 = rag_controller._generate_cache_key(query1)
        key2 = rag_controller._generate_cache_key(query2)
        key3 = rag_controller._generate_cache_key(query3)
        
        assert key1 == key2  # 相同查询应生成相同键
        assert key1 != key3  # 不同查询应生成不同键
        assert key1.startswith("query:")
    
    async def test_config_validation(self):
        """测试配置验证"""
        # 测试默认配置
        config = RAGConfig()
        assert config.enable_cache is True
        assert config.cache_ttl > 0
        assert config.max_context_length > 0
        
        # 测试自定义配置
        custom_config = RAGConfig(
            enable_cache=False,
            cache_ttl=1800,
            max_context_length=4000
        )
        assert custom_config.enable_cache is False
        assert custom_config.cache_ttl == 1800
        assert custom_config.max_context_length == 4000
    
    async def test_concurrent_queries(self, rag_controller, sample_query_request):
        """测试并发查询处理"""
        import asyncio
        
        # 模拟流水线处理
        mock_response = RAGResponse(
            response="并发测试响应",
            sources=[],
            confidence=0.8,
            context="测试上下文",
            metadata={}
        )
        rag_controller.pipeline.process = AsyncMock(return_value=mock_response)
        
        # 创建多个并发查询
        queries = [sample_query_request for _ in range(5)]
        tasks = [rag_controller.query(query) for query in queries]
        
        # 执行并发查询
        responses = await asyncio.gather(*tasks)
        
        assert len(responses) == 5
        assert all(response.success for response in responses)
        assert rag_controller.metrics['total_queries'] == 5
    
    @pytest.mark.parametrize("enable_cache,expected_cache_calls", [
        (True, 1),
        (False, 0),
    ])
    async def test_cache_configuration(self, rag_controller, sample_query_request, 
                                     enable_cache, expected_cache_calls):
        """测试缓存配置"""
        # 设置缓存配置
        rag_controller.config.enable_cache = enable_cache
        
        # 模拟缓存和流水线
        rag_controller.query_cache.get_cached_response = AsyncMock(return_value=None)
        rag_controller.query_cache.cache_response = AsyncMock(return_value=True)
        
        mock_response = RAGResponse(
            response="测试响应",
            sources=[],
            confidence=0.8,
            context="测试上下文",
            metadata={}
        )
        rag_controller.pipeline.process = AsyncMock(return_value=mock_response)
        
        # 执行查询
        await rag_controller.query(sample_query_request)
        
        # 验证缓存调用次数
        assert rag_controller.query_cache.get_cached_response.call_count == expected_cache_calls
