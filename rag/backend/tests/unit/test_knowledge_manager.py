"""
知识库管理器单元测试
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent if Path(__file__).parent.name != "backend" else Path(__file__).parent
sys.path.insert(0, str(project_root))

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import AsyncMock, patch, mock_open

from core.knowledge import KnowledgeManager
from models.document import Document, DocumentMetadata, DocumentChunk


@pytest.mark.unit
@pytest.mark.asyncio
class TestKnowledgeManager:
    """知识库管理器测试类"""
    
    async def test_initialization(self, knowledge_manager):
        """测试初始化"""
        assert knowledge_manager is not None
        assert knowledge_manager.db_manager is not None
        assert knowledge_manager.vector_store is not None
        assert knowledge_manager.model_manager is not None
    
    async def test_generate_document_id(self, knowledge_manager):
        """测试文档ID生成"""
        file_path = "/test/path/document.pdf"
        doc_id = knowledge_manager._generate_document_id(file_path)
        
        assert doc_id is not None
        assert isinstance(doc_id, str)
        assert len(doc_id) > 0
        
        # 相同路径应生成相同ID
        doc_id2 = knowledge_manager._generate_document_id(file_path)
        assert doc_id == doc_id2
    
    async def test_split_document(self, knowledge_manager, sample_document_content):
        """测试文档分割"""
        # 创建测试文档
        document = Document(
            id="test_doc_1",
            content=sample_document_content['content'],
            metadata=DocumentMetadata(**sample_document_content['metadata'])
        )
        
        # 分割文档
        chunks = await knowledge_manager._split_document(document)
        
        assert len(chunks) > 0
        assert all(isinstance(chunk, DocumentChunk) for chunk in chunks)
        assert all(chunk.document_id == document.id for chunk in chunks)
        assert all(len(chunk.content) > 0 for chunk in chunks)
        
        # 检查块的索引
        for i, chunk in enumerate(chunks):
            assert chunk.metadata.get('chunk_index') == i
    
    async def test_add_document_success(self, knowledge_manager, temp_dir):
        """测试成功添加文档"""
        # 创建测试文件
        test_file = temp_dir / "test.txt"
        test_content = "这是一个测试文档内容。包含人工智能相关信息。"
        test_file.write_text(test_content, encoding='utf-8')
        
        # 添加文档
        success = await knowledge_manager.add_document(
            str(test_file),
            metadata={'test': True}
        )
        
        assert success is True
        
        # 验证数据库调用
        knowledge_manager.db_manager.save_document.assert_called_once()
        knowledge_manager.db_manager.save_document_chunks.assert_called_once()
        knowledge_manager.db_manager.update_document_status.assert_called()
        
        # 验证向量存储调用
        knowledge_manager.vector_store.add_documents.assert_called_once()
    
    async def test_add_document_file_not_found(self, knowledge_manager):
        """测试文件不存在的情况"""
        success = await knowledge_manager.add_document("/nonexistent/file.txt")
        assert success is False
    
    async def test_add_document_unsupported_format(self, knowledge_manager, temp_dir):
        """测试不支持的文件格式"""
        # 创建不支持的文件格式
        test_file = temp_dir / "test.xyz"
        test_file.write_text("test content")
        
        success = await knowledge_manager.add_document(str(test_file))
        assert success is False
    
    async def test_remove_document_success(self, knowledge_manager):
        """测试成功移除文档"""
        document_id = "test_doc_1"
        
        # 模拟文档存在
        knowledge_manager.db_manager.get_document.return_value = {
            'id': document_id,
            'filename': 'test.pdf'
        }
        
        success = await knowledge_manager.remove_document(document_id)
        assert success is True
        
        # 验证调用
        knowledge_manager.db_manager.delete_document.assert_called_once_with(document_id)
        knowledge_manager.vector_store.delete_documents.assert_called_once()
    
    async def test_remove_document_not_found(self, knowledge_manager):
        """测试移除不存在的文档"""
        document_id = "nonexistent_doc"
        
        # 模拟文档不存在
        knowledge_manager.db_manager.get_document.return_value = None
        
        success = await knowledge_manager.remove_document(document_id)
        assert success is False
    
    async def test_search_documents_with_embedding(self, knowledge_manager):
        """测试使用向量搜索文档"""
        query = "人工智能"
        query_embedding = [0.1] * 768
        
        results = await knowledge_manager.search_documents(
            query, 
            top_k=5, 
            query_embedding=query_embedding
        )
        
        assert isinstance(results, list)
        assert len(results) <= 5
        
        # 验证向量存储调用
        knowledge_manager.vector_store.search_similar.assert_called_once_with(
            query_embedding, 5
        )
    
    async def test_search_documents_text_fallback(self, knowledge_manager):
        """测试文本搜索后备方案"""
        query = "人工智能"
        
        # 添加一些测试数据到内存缓存
        from models.document import DocumentChunk
        test_chunks = [
            DocumentChunk(
                id="chunk_1",
                document_id="doc_1",
                content="人工智能是计算机科学的分支",
                start_index=0,
                end_index=50,
                metadata={'chunk_index': 0}
            ),
            DocumentChunk(
                id="chunk_2",
                document_id="doc_2",
                content="机器学习是AI的重要技术",
                start_index=0,
                end_index=40,
                metadata={'chunk_index': 0}
            )
        ]
        
        knowledge_manager.document_chunks = {
            "doc_1": [test_chunks[0]],
            "doc_2": [test_chunks[1]]
        }
        
        results = await knowledge_manager.search_documents(query, top_k=5)
        
        assert isinstance(results, list)
        assert len(results) > 0
        assert all(query in result.content for result in results)
    
    async def test_get_statistics(self, knowledge_manager):
        """测试获取统计信息"""
        stats = await knowledge_manager.get_statistics()
        
        assert isinstance(stats, dict)
        assert 'document_count' in stats
        assert 'chunk_count' in stats
        assert 'vector_count' in stats
        
        # 验证数据库调用
        knowledge_manager.db_manager.get_statistics.assert_called_once()
    
    async def test_health_check(self, knowledge_manager):
        """测试健康检查"""
        health = await knowledge_manager.health_check()
        assert health is True
        
        # 验证各组件健康检查调用
        knowledge_manager.db_manager.health_check.assert_called_once()
        knowledge_manager.vector_store.health_check.assert_called_once()
    
    async def test_vectorize_and_store_chunks(self, knowledge_manager):
        """测试向量化和存储"""
        from models.document import DocumentChunk
        
        chunks = [
            DocumentChunk(
                id="chunk_1",
                document_id="doc_1",
                content="测试内容1",
                start_index=0,
                end_index=10,
                metadata={'chunk_index': 0}
            ),
            DocumentChunk(
                id="chunk_2",
                document_id="doc_1",
                content="测试内容2",
                start_index=10,
                end_index=20,
                metadata={'chunk_index': 1}
            )
        ]
        
        # 执行向量化和存储
        await knowledge_manager._vectorize_and_store_chunks(chunks)
        
        # 验证模型管理器调用
        knowledge_manager.model_manager.create_embeddings.assert_called_once()
        
        # 验证向量存储调用
        knowledge_manager.vector_store.add_documents.assert_called_once()
    
    async def test_generate_mock_embedding(self, knowledge_manager):
        """测试模拟向量生成"""
        text = "测试文本内容"
        embedding = knowledge_manager._generate_mock_embedding(text)
        
        assert isinstance(embedding, list)
        assert len(embedding) == 768  # 默认维度
        assert all(isinstance(x, float) for x in embedding)
        
        # 相同文本应生成相同向量
        embedding2 = knowledge_manager._generate_mock_embedding(text)
        assert embedding == embedding2
    
    @pytest.mark.parametrize("file_extension,expected_available", [
        ('.pdf', True),
        ('.txt', True),
        ('.docx', True),
        ('.doc', True),
        ('.xyz', False),
    ])
    async def test_processor_selection(self, knowledge_manager, file_extension, expected_available):
        """测试文档处理器选择"""
        processor = knowledge_manager.processors.get(file_extension)
        if expected_available:
            assert processor is not None
        else:
            assert processor is None
