# 📚 RAG Backend API 文档

## 🌐 基础信息

- **Base URL**: `http://localhost:8000`
- **API Version**: `v1`
- **Content-Type**: `application/json`
- **Authentication**: 暂不需要（开发阶段）

## 📋 接口概览

| 分类 | 接口 | 方法 | 描述 |
|------|------|------|------|
| 🤖 RAG | `/api/v1/rag/query` | POST | 执行RAG查询 |
| 📚 知识库 | `/api/v1/knowledge/upload` | POST | 上传文档 |
| 📚 知识库 | `/api/v1/knowledge/status` | GET | 获取知识库状态 |
| 📚 知识库 | `/api/v1/knowledge/build` | POST | 构建知识库索引 |
| 🏥 系统 | `/api/v1/health` | GET | 健康检查 |
| 📊 系统 | `/api/v1/statistics` | GET | 系统统计信息 |
| 📈 系统 | `/api/v1/metrics` | GET | 性能指标 |

## 🤖 RAG查询接口

### POST `/api/v1/rag/query`

执行检索增强生成查询。

#### 请求参数

```json
{
  "query": "什么是人工智能？",
  "query_type": "general",
  "top_k": 5,
  "filters": {
    "document_type": "pdf",
    "date_range": {
      "start": "2024-01-01",
      "end": "2024-12-31"
    }
  },
  "options": {
    "enable_cache": true,
    "temperature": 0.7,
    "max_tokens": 2000
  }
}
```

#### 参数说明

| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `query` | string | ✅ | 用户查询内容 |
| `query_type` | string | ❌ | 查询类型：`general`, `risk_assessment`, `compliance_check`, `summary` |
| `top_k` | integer | ❌ | 返回相关文档数量，默认5 |
| `filters` | object | ❌ | 过滤条件 |
| `options` | object | ❌ | 查询选项 |

#### 响应示例

```json
{
  "success": true,
  "response": "人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统...",
  "sources": [
    {
      "document_id": "doc_123",
      "title": "人工智能概述",
      "content": "人工智能是计算机科学的分支...",
      "score": 0.95,
      "metadata": {
        "file_name": "ai_overview.pdf",
        "page": 1,
        "created_at": "2024-01-01T00:00:00Z"
      }
    }
  ],
  "confidence": 0.92,
  "query_id": "query_456",
  "processing_time": 1.23,
  "from_cache": false,
  "metadata": {
    "total_documents_searched": 1000,
    "retrieval_time": 0.45,
    "generation_time": 0.78
  }
}
```

#### 错误响应

```json
{
  "success": false,
  "error": {
    "code": "INVALID_QUERY",
    "message": "查询内容不能为空",
    "details": {}
  },
  "query_id": "query_456"
}
```

## 📚 知识库管理接口

### POST `/api/v1/knowledge/upload`

上传文档到知识库。

#### 请求参数

```bash
curl -X POST "http://localhost:8000/api/v1/knowledge/upload" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@document.pdf" \
     -F "metadata={\"category\":\"technical\",\"tags\":[\"AI\",\"ML\"]}"
```

#### 响应示例

```json
{
  "success": true,
  "message": "文档上传成功",
  "document_id": "doc_789",
  "filename": "document.pdf",
  "file_size": 1024000,
  "processing_status": "queued",
  "estimated_processing_time": 30
}
```

### GET `/api/v1/knowledge/status`

获取知识库状态信息。

#### 响应示例

```json
{
  "status": "ready",
  "document_count": 1250,
  "total_chunks": 15000,
  "index_size": "2.5GB",
  "last_updated": "2024-01-01T12:00:00Z",
  "processing_queue": {
    "pending": 3,
    "processing": 1,
    "failed": 0
  }
}
```

### POST `/api/v1/knowledge/build`

重新构建知识库索引。

#### 请求参数

```json
{
  "force_rebuild": false,
  "document_ids": ["doc_123", "doc_456"],
  "options": {
    "chunk_size": 1000,
    "chunk_overlap": 200
  }
}
```

#### 响应示例

```json
{
  "success": true,
  "message": "索引构建已开始",
  "job_id": "build_job_123",
  "estimated_time": 300
}
```

## 🏥 系统接口

### GET `/api/v1/health`

系统健康检查。

#### 响应示例

```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "components": {
    "database": {
      "status": "healthy",
      "response_time": 0.05
    },
    "vector_store": {
      "status": "healthy",
      "response_time": 0.12
    },
    "cache": {
      "status": "healthy",
      "response_time": 0.02
    },
    "ai_models": {
      "status": "healthy",
      "response_time": 0.89
    }
  },
  "uptime": 86400,
  "version": "1.0.0"
}
```

### GET `/api/v1/statistics`

获取系统统计信息。

#### 响应示例

```json
{
  "requests": {
    "total": 10000,
    "today": 500,
    "success_rate": 0.98
  },
  "documents": {
    "total": 1250,
    "processed": 1200,
    "failed": 50
  },
  "cache": {
    "hit_rate": 0.85,
    "total_hits": 8500,
    "total_misses": 1500
  },
  "performance": {
    "avg_response_time": 1.2,
    "p95_response_time": 2.5,
    "p99_response_time": 5.0
  }
}
```

### GET `/api/v1/metrics`

获取详细性能指标。

#### 响应示例

```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "metrics": {
    "requests_total": 10000,
    "requests_per_second": 10.5,
    "response_time_histogram": {
      "0.1": 1000,
      "0.5": 5000,
      "1.0": 8000,
      "2.0": 9500,
      "5.0": 9900
    },
    "error_rate": 0.02,
    "cache_hit_rate": 0.85,
    "ai_model_calls": {
      "total": 5000,
      "success": 4950,
      "failed": 50
    },
    "memory_usage": {
      "used": "1.2GB",
      "total": "4GB",
      "percentage": 0.3
    }
  }
}
```

## 🚨 错误码说明

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| `INVALID_QUERY` | 400 | 查询参数无效 |
| `EMPTY_QUERY` | 400 | 查询内容为空 |
| `QUERY_TOO_LONG` | 400 | 查询内容过长 |
| `UNSUPPORTED_FILE_TYPE` | 400 | 不支持的文件类型 |
| `FILE_TOO_LARGE` | 400 | 文件过大 |
| `KNOWLEDGE_BASE_EMPTY` | 404 | 知识库为空 |
| `DOCUMENT_NOT_FOUND` | 404 | 文档不存在 |
| `AI_MODEL_ERROR` | 500 | AI模型调用失败 |
| `DATABASE_ERROR` | 500 | 数据库错误 |
| `CACHE_ERROR` | 500 | 缓存错误 |
| `INTERNAL_ERROR` | 500 | 内部服务器错误 |

## 📝 使用示例

### Python客户端示例

```python
import requests
import json

# RAG查询
def rag_query(query, query_type="general", top_k=5):
    url = "http://localhost:8000/api/v1/rag/query"
    payload = {
        "query": query,
        "query_type": query_type,
        "top_k": top_k
    }
    
    response = requests.post(url, json=payload)
    return response.json()

# 文档上传
def upload_document(file_path, metadata=None):
    url = "http://localhost:8000/api/v1/knowledge/upload"
    
    with open(file_path, 'rb') as f:
        files = {'file': f}
        data = {'metadata': json.dumps(metadata or {})}
        response = requests.post(url, files=files, data=data)
    
    return response.json()

# 使用示例
result = rag_query("什么是机器学习？")
print(result['response'])
```

### JavaScript客户端示例

```javascript
// RAG查询
async function ragQuery(query, queryType = 'general', topK = 5) {
    const response = await fetch('http://localhost:8000/api/v1/rag/query', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            query: query,
            query_type: queryType,
            top_k: topK
        })
    });
    
    return await response.json();
}

// 使用示例
ragQuery('什么是深度学习？').then(result => {
    console.log(result.response);
});
```

## 🔄 版本更新

### v1.0.0 (当前版本)
- ✅ 基础RAG查询功能
- ✅ 文档上传和处理
- ✅ 知识库管理
- ✅ 系统监控和健康检查

### 计划中的功能
- 🔄 用户认证和权限管理
- 🔄 多租户支持
- 🔄 高级检索策略
- 🔄 对话历史管理
- 🔄 批量文档处理

---

**📞 如有问题，请查看 [FAQ](FAQ.md) 或提交 [Issue](issues/)**
