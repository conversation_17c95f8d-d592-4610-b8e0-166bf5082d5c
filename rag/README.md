# RAG系统 - 检索增强生成

这是AI合同审查系统的RAG（Retrieval-Augmented Generation）子系统，负责提供智能检索和知识增强功能。

## 📁 项目结构

```
rag/
├── backend/                   # RAG后端服务
│   ├── api/                  # API路由和接口
│   │   ├── __init__.py
│   │   ├── routes.py         # API路由
│   │   ├── schemas.py        # 数据模式
│   │   └── middleware.py     # 中间件
│   ├── core/                 # 核心业务逻辑
│   │   ├── __init__.py
│   │   ├── controller.py     # RAG控制器
│   │   ├── pipeline.py       # RAG流水线
│   │   ├── knowledge/        # 知识库管理
│   │   ├── retrieval/        # 检索引擎
│   │   ├── generation/       # 生成增强
│   │   └── storage/          # 存储层
│   ├── config/               # 配置管理
│   │   ├── __init__.py
│   │   ├── settings.py       # 配置管理
│   │   ├── models.yaml       # 模型配置
│   │   └── prompts/          # 提示模板
│   ├── utils/                # 工具函数
│   │   ├── __init__.py
│   │   ├── text_processing.py # 文本处理工具
│   │   ├── metrics.py        # 评估指标
│   │   ├── monitoring.py     # 监控工具
│   │   └── logger.py         # 日志工具
│   ├── models/               # 数据模型
│   │   ├── __init__.py
│   │   ├── document.py       # 文档模型
│   │   ├── query.py          # 查询模型
│   │   └── response.py       # 响应模型
│   ├── services/             # 业务服务
│   │   ├── __init__.py
│   │   ├── knowledge_service.py # 知识库服务
│   │   ├── retrieval_service.py # 检索服务
│   │   └── generation_service.py # 生成服务
│   ├── tests/                # 测试代码
│   │   ├── __init__.py
│   │   ├── test_knowledge.py # 知识库测试
│   │   ├── test_retrieval.py # 检索测试
│   │   └── test_generation.py # 生成测试
│   ├── docker/               # Docker配置
│   │   ├── Dockerfile
│   │   ├── docker-compose.yml
│   │   └── .dockerignore
│   ├── main.py               # 后端主入口
│   └── requirements.txt      # Python依赖
├── frontend/                 # RAG前端界面
│   ├── src/                  # 源代码
│   │   ├── components/       # Vue组件
│   │   │   ├── common/       # 通用组件
│   │   │   ├── document/     # 文档相关组件
│   │   │   ├── query/        # 查询相关组件
│   │   │   └── result/       # 结果展示组件
│   │   ├── views/            # 页面视图
│   │   │   ├── Dashboard.vue # 仪表板
│   │   │   ├── DocumentManager.vue # 文档管理
│   │   │   ├── QueryInterface.vue # 查询界面
│   │   │   └── KnowledgeBase.vue # 知识库浏览
│   │   ├── api/              # API调用
│   │   │   ├── index.ts      # API配置
│   │   │   ├── document.ts   # 文档API
│   │   │   ├── query.ts      # 查询API
│   │   │   └── knowledge.ts  # 知识库API
│   │   ├── utils/            # 工具函数
│   │   │   ├── request.ts    # 请求工具
│   │   │   ├── format.ts     # 格式化工具
│   │   │   └── validation.ts # 验证工具
│   │   ├── stores/           # 状态管理
│   │   │   ├── index.ts      # Store配置
│   │   │   ├── document.ts   # 文档状态
│   │   │   └── query.ts      # 查询状态
│   │   ├── types/            # TypeScript类型
│   │   │   ├── api.ts        # API类型
│   │   │   ├── document.ts   # 文档类型
│   │   │   └── query.ts      # 查询类型
│   │   ├── assets/           # 静态资源
│   │   │   ├── css/          # 样式文件
│   │   │   ├── images/       # 图片资源
│   │   │   └── icons/        # 图标资源
│   │   ├── App.vue           # 根组件
│   │   └── main.ts           # 前端入口
│   ├── public/               # 公共文件
│   │   ├── index.html        # HTML模板
│   │   └── favicon.ico       # 网站图标
│   ├── package.json          # 前端依赖
│   ├── vite.config.ts        # Vite配置
│   ├── tsconfig.json         # TypeScript配置
│   └── .env.example          # 环境变量示例
└── README.md                 # 本文档
```

## 🚀 快速开始

### 后端服务启动

```bash
# 1. 进入后端目录
cd rag/backend

# 2. 安装Python依赖
pip install -r requirements.txt

# 3. 配置环境变量
cp .env.example .env
# 编辑.env文件，配置API密钥、数据库连接等

# 4. 启动RAG后端服务
python main.py

# 或使用Docker启动
docker-compose up -d
```

### 前端界面启动

```bash
# 1. 进入前端目录
cd rag/frontend

# 2. 安装Node.js依赖
npm install

# 3. 配置环境变量
cp .env.example .env
# 编辑.env文件，配置后端API地址等

# 4. 启动前端开发服务器
npm run dev

# 构建生产版本
npm run build
```

### 访问地址

- **后端API服务**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **前端界面**: http://localhost:3000

## 🏗️ 核心功能

### 知识库管理
- 多格式文档摄取（PDF、DOCX、TXT）
- 智能文本切分和向量化
- 增量索引更新
- 知识图谱构建

### 检索引擎
- 向量相似度检索
- 关键词精确匹配
- 混合检索策略
- 智能重排序

### 生成增强
- 上下文智能构建
- 提示模板管理
- Token优化管理
- 质量控制和验证

### 性能优化
- 多级缓存策略
- 异步并行处理
- 批量操作优化
- 实时监控告警

## 📊 性能指标

- **检索精度**: Recall@10 > 90%
- **检索速度**: 平均响应时间 < 200ms
- **生成质量**: 幻觉率 < 5%
- **系统可用性**: > 99.5%

## 🔧 配置说明

### 向量存储配置
```yaml
vector_store:
  provider: "chroma"  # chroma, weaviate, pinecone
  collection_name: "contract_knowledge"
  distance_metric: "cosine"
```

### 检索配置
```yaml
retrieval:
  strategy: "hybrid"  # vector_only, keyword_only, hybrid
  top_k: 10
  similarity_threshold: 0.7
  enable_rerank: true
```

### 生成配置
```yaml
generation:
  max_context_length: 4000
  enable_hallucination_detection: true
  enable_citation_generation: true
```

## 🧪 测试

```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_retrieval.py

# 性能基准测试
python scripts/benchmark.py
```

## 📈 监控

RAG系统提供完整的监控和日志功能：

- **性能监控**: 延迟、吞吐量、准确率
- **错误监控**: 异常率、失败原因分析
- **资源监控**: CPU、内存、存储使用情况
- **业务监控**: 查询类型、热点数据分析

## 🔗 API接口

### 检索接口
```http
POST /api/v1/retrieve
Content-Type: application/json

{
  "query": "合同风险评估",
  "top_k": 10,
  "filters": {"document_type": "contract"}
}
```

### 生成接口
```http
POST /api/v1/generate
Content-Type: application/json

{
  "query": "分析这份合同的主要风险",
  "context": "...",
  "task_type": "risk_assessment"
}
```

### RAG完整流程接口
```http
POST /api/v1/rag
Content-Type: application/json

{
  "query": "这份合同有哪些法律风险？",
  "document_id": "contract_123",
  "task_type": "risk_assessment"
}
```

## 🚀 部署

### Docker部署
```bash
cd rag
docker-compose up -d
```

### 生产环境部署
```bash
# 使用Kubernetes
kubectl apply -f k8s/

# 或使用Docker Swarm
docker stack deploy -c docker-compose.prod.yml rag-stack
```

## 📚 相关文档

- [架构设计文档](docs/architecture.md)
- [API参考文档](docs/api_reference.md)
- [部署指南](docs/deployment.md)
- [性能调优指南](docs/performance.md)

## 🤝 贡献指南

1. Fork项目
2. 创建特性分支
3. 提交代码变更
4. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情。

---

**RAG系统版本**: v1.0.0  
**最后更新**: 2024年  
**维护状态**: 活跃开发中
