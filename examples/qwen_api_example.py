"""
千问API使用示例
演示如何使用阿里云百炼平台的千问模型进行合同分析
"""

import asyncio
import os
from typing import Dict, Any

# 设置API Key
os.environ["DASHSCOPE_API_KEY"] = "your-dashscope-api-key-here"

from backend.app.services.qwen_api_service import QwenAPIService, QwenModelType


async def main():
    """主函数演示千问API的各种用法"""
    
    # 初始化千问API服务
    qwen_service = QwenAPIService()
    
    # 示例合同文本
    sample_contract = """
    甲方：北京科技有限公司
    乙方：上海贸易有限公司
    
    根据《中华人民共和国合同法》等相关法律法规，甲乙双方本着平等、自愿、公平、诚实信用的原则，
    就软件开发服务事宜达成如下协议：
    
    第一条 服务内容
    甲方委托乙方开发一套企业管理系统，包括用户管理、权限管理、数据统计等功能模块。
    
    第二条 服务期限
    本合同服务期限为2024年1月1日至2024年12月31日。
    
    第三条 服务费用
    本项目总费用为人民币50万元整，分三期支付：
    1. 合同签订后支付30%，即15万元
    2. 项目中期验收后支付40%，即20万元  
    3. 项目最终验收后支付30%，即15万元
    
    第四条 知识产权
    项目开发的所有代码和文档的知识产权归甲方所有。
    
    第五条 违约责任
    任何一方违约，应承担相应的违约责任，并赔偿对方因此造成的损失。
    """
    
    print("=== 千问API合同分析示例 ===\n")
    
    # 1. 基础聊天完成示例
    print("1. 基础聊天完成示例")
    print("-" * 50)
    
    messages = [
        {"role": "system", "content": "你是一位专业的法律顾问。"},
        {"role": "user", "content": "请简要介绍合同法的基本原则。"}
    ]
    
    response = await qwen_service.chat_completion(messages, task_type="general")
    print(f"模型: {response.model}")
    print(f"回答: {response.content}")
    print(f"Token使用: {response.usage}")
    print()
    
    # 2. 合同摘要生成
    print("2. 合同摘要生成")
    print("-" * 50)
    
    summary = await qwen_service.generate_contract_summary(sample_contract)
    print(f"合同摘要:\n{summary}")
    print()
    
    # 3. 风险评估
    print("3. 风险评估")
    print("-" * 50)
    
    risks = await qwen_service.assess_contract_risks(sample_contract)
    if "error" not in risks:
        print("风险评估结果:")
        for risk_type, details in risks.items():
            if isinstance(details, dict):
                print(f"  {risk_type}: {details.get('level', 'N/A')} - {details.get('description', 'N/A')}")
    else:
        print(f"风险评估失败: {risks['error']}")
    print()
    
    # 4. 条款分析
    print("4. 条款分析")
    print("-" * 50)
    
    clauses = await qwen_service.analyze_contract_clauses(sample_contract)
    if clauses and "error" not in clauses[0]:
        print("条款分析结果:")
        for i, clause in enumerate(clauses[:3]):  # 只显示前3个
            print(f"  条款{i+1}: {clause.get('type', 'N/A')} - {clause.get('analysis', 'N/A')}")
    else:
        print(f"条款分析失败: {clauses[0].get('error', 'Unknown error')}")
    print()
    
    # 5. 合规检查
    print("5. 合规检查")
    print("-" * 50)
    
    compliance = await qwen_service.check_compliance(sample_contract)
    if "error" not in compliance:
        print(f"合规等级: {compliance.get('level', 'N/A')}")
        print(f"主要问题: {compliance.get('issues', 'N/A')}")
    else:
        print(f"合规检查失败: {compliance['error']}")
    print()
    
    # 6. 并行分析（推荐）
    print("6. 并行分析（推荐使用）")
    print("-" * 50)
    
    print("正在执行并行分析...")
    parallel_results = await qwen_service.parallel_analysis(sample_contract)
    
    print(f"分析状态: {parallel_results.get('status', 'unknown')}")
    print(f"摘要长度: {len(parallel_results.get('summary', ''))}")
    print(f"风险类别数: {len(parallel_results.get('risks', {}))}")
    print(f"条款数量: {len(parallel_results.get('clauses', []))}")
    print(f"合规检查: {'完成' if parallel_results.get('compliance') else '失败'}")
    print()
    
    # 7. 文本向量化示例
    print("7. 文本向量化示例")
    print("-" * 50)
    
    texts = [
        "这是一份软件开发合同",
        "合同总金额为50万元",
        "项目开发周期为一年"
    ]
    
    embeddings = await qwen_service.embed_texts(texts)
    if embeddings:
        print(f"成功生成 {len(embeddings)} 个向量")
        print(f"向量维度: {len(embeddings[0])}")
    else:
        print("向量化失败")
    print()
    
    # 8. 不同模型对比
    print("8. 不同模型效果对比")
    print("-" * 50)
    
    test_question = "这份合同的主要风险是什么？"
    models_to_test = ["flash", "plus", "max"]
    
    for model_type in models_to_test:
        messages = [
            {"role": "system", "content": "你是专业的法律顾问，请简洁回答。"},
            {"role": "user", "content": f"基于以下合同，{test_question}\n\n{sample_contract[:500]}..."}
        ]
        
        # 手动指定模型类型
        qwen_service.task_model_mapping["test"] = getattr(QwenModelType, model_type.upper())
        response = await qwen_service.chat_completion(messages, task_type="test")
        
        print(f"{model_type.upper()}模型回答:")
        print(f"  内容: {response.content[:100]}...")
        print(f"  Token: {response.usage}")
        print()
    
    print("=== 示例完成 ===")


def sync_example():
    """同步调用示例"""
    print("=== 同步调用示例 ===")
    
    # 使用OpenAI兼容接口进行同步调用
    from openai import OpenAI
    
    client = OpenAI(
        api_key=os.environ.get("DASHSCOPE_API_KEY"),
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
    )
    
    response = client.chat.completions.create(
        model="qwen-plus",
        messages=[
            {"role": "system", "content": "你是专业的法律助手。"},
            {"role": "user", "content": "请解释什么是合同的要约和承诺？"}
        ],
        temperature=0.1,
        max_tokens=500
    )
    
    print(f"回答: {response.choices[0].message.content}")
    print(f"Token使用: {response.usage}")


if __name__ == "__main__":
    # 检查API Key
    if not os.environ.get("DASHSCOPE_API_KEY") or os.environ.get("DASHSCOPE_API_KEY") == "your-dashscope-api-key-here":
        print("请先设置DASHSCOPE_API_KEY环境变量")
        print("获取API Key: https://bailian.console.aliyun.com/")
        exit(1)
    
    # 运行异步示例
    asyncio.run(main())
    
    # 运行同步示例
    sync_example()
