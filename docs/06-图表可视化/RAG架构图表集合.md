# RAG系统架构图表集合

本文档包含RAG系统的各种架构图表，使用Mermaid语法绘制，便于理解系统设计和组件关系。

## 1. 系统整体架构图

```mermaid
graph TB
    subgraph "用户层"
        U1[Web浏览器]
        U2[移动应用]
        U3[API客户端]
    end
    
    subgraph "API网关层"
        GW[Nginx负载均衡器]
    end
    
    subgraph "服务层"
        subgraph "RAG核心服务集群"
            RS1[RAG服务-1]
            RS2[RAG服务-2]
            RS3[RAG服务-3]
        end
        
        subgraph "专业服务"
            KS[知识库服务]
            RET[检索服务]
            GEN[生成服务]
            QC[质量控制服务]
        end
    end
    
    subgraph "数据层"
        subgraph "向量数据库"
            C1[ChromaDB-1]
            C2[ChromaDB-2]
        end
        
        subgraph "缓存层"
            R1[Redis-1]
            R2[Redis-2]
            R3[Redis-3]
        end
        
        subgraph "关系数据库"
            PM[PostgreSQL主]
            PS[PostgreSQL从]
        end
        
        FS[文件存储]
    end
    
    subgraph "外部服务"
        LLM[LLM API网关]
        EMB[嵌入模型API]
    end
    
    subgraph "监控层"
        MON[Prometheus]
        GRAF[Grafana]
        LOG[ELK Stack]
    end
    
    U1 --> GW
    U2 --> GW
    U3 --> GW
    
    GW --> RS1
    GW --> RS2
    GW --> RS3
    
    RS1 --> KS
    RS1 --> RET
    RS1 --> GEN
    RS1 --> QC
    
    KS --> C1
    KS --> C2
    KS --> PM
    KS --> FS
    
    RET --> C1
    RET --> C2
    RET --> R1
    
    GEN --> LLM
    GEN --> R2
    
    QC --> PM
    QC --> R3
    
    PM --> PS
    R1 --> R2
    R2 --> R3
    
    RS1 --> MON
    RS2 --> MON
    RS3 --> MON
    
    MON --> GRAF
    MON --> LOG
```

## 2. RAG处理流程图

```mermaid
flowchart TD
    START([用户查询]) --> QU[查询理解]
    QU --> QE[查询扩展]
    QE --> CACHE{缓存检查}
    
    CACHE -->|命中| RETURN[返回缓存结果]
    CACHE -->|未命中| RET[检索执行]
    
    RET --> VR[向量检索]
    RET --> KR[关键词检索]
    RET --> SR[语义检索]
    
    VR --> MERGE[结果融合]
    KR --> MERGE
    SR --> MERGE
    
    MERGE --> RERANK[智能重排序]
    RERANK --> CTX[上下文构建]
    
    CTX --> PROMPT[提示构建]
    PROMPT --> LLM[LLM生成]
    
    LLM --> QA[质量评估]
    QA --> HALL{幻觉检测}
    
    HALL -->|通过| CIT[引用生成]
    HALL -->|失败| RETRY[重新生成]
    
    RETRY --> PROMPT
    CIT --> CACHE_SET[设置缓存]
    CACHE_SET --> END([返回结果])
    RETURN --> END
```

## 3. 知识库管理架构图

```mermaid
graph TB
    subgraph "文档输入"
        PDF[PDF文档]
        DOCX[Word文档]
        TXT[文本文档]
        HTML[HTML文档]
    end
    
    subgraph "文档处理流水线"
        VALID[文档验证]
        EXTRACT[内容提取]
        CLEAN[文本清理]
        STRUCT[结构分析]
        META[元数据提取]
        SPLIT[智能分割]
        VEC[向量化]
        INDEX[索引构建]
    end
    
    subgraph "存储层"
        VSTORE[向量存储]
        MSTORE[元数据存储]
        FSTORE[文件存储]
        CACHE[缓存层]
    end
    
    subgraph "管理接口"
        API[管理API]
        MONITOR[处理监控]
        HEALTH[健康检查]
    end
    
    PDF --> VALID
    DOCX --> VALID
    TXT --> VALID
    HTML --> VALID
    
    VALID --> EXTRACT
    EXTRACT --> CLEAN
    CLEAN --> STRUCT
    STRUCT --> META
    META --> SPLIT
    SPLIT --> VEC
    VEC --> INDEX
    
    INDEX --> VSTORE
    META --> MSTORE
    EXTRACT --> FSTORE
    VEC --> CACHE
    
    API --> VALID
    MONITOR --> EXTRACT
    MONITOR --> VEC
    HEALTH --> VSTORE
    HEALTH --> MSTORE
```

## 4. 检索引擎架构图

```mermaid
graph LR
    subgraph "查询处理"
        Q[用户查询]
        QP[查询预处理]
        QE[查询扩展]
        QR[查询重写]
    end
    
    subgraph "检索策略"
        DR[密集检索<br/>Dense Retrieval]
        SR[稀疏检索<br/>Sparse Retrieval]
        SEM[语义检索<br/>Semantic Retrieval]
    end
    
    subgraph "数据源"
        VDB[(向量数据库)]
        IDX[(倒排索引)]
        KG[(知识图谱)]
    end
    
    subgraph "结果处理"
        MERGE[结果融合]
        DEDUP[去重处理]
        RERANK[重排序]
        FILTER[结果过滤]
    end
    
    subgraph "缓存层"
        QC[查询缓存]
        RC[结果缓存]
        EC[嵌入缓存]
    end
    
    Q --> QP
    QP --> QE
    QE --> QR
    
    QR --> DR
    QR --> SR
    QR --> SEM
    
    DR --> VDB
    SR --> IDX
    SEM --> KG
    
    VDB --> MERGE
    IDX --> MERGE
    KG --> MERGE
    
    MERGE --> DEDUP
    DEDUP --> RERANK
    RERANK --> FILTER
    
    QC --> QR
    RC --> FILTER
    EC --> DR
```

## 5. 生成引擎架构图

```mermaid
graph TD
    subgraph "输入处理"
        QUERY[用户查询]
        DOCS[检索文档]
        CONTEXT[上下文构建]
    end
    
    subgraph "提示工程"
        TEMPLATE[模板选择]
        PROMPT[提示构建]
        OPTIMIZE[提示优化]
    end
    
    subgraph "LLM网关"
        GATEWAY[LLM网关]
        BALANCE[负载均衡]
        FALLBACK[降级处理]
    end
    
    subgraph "模型服务"
        GPT4[GPT-4 Turbo]
        QWEN[千问模型]
        LOCAL[本地模型]
    end
    
    subgraph "后处理"
        PARSE[响应解析]
        VALIDATE[结果验证]
        FORMAT[格式化]
    end
    
    subgraph "质量控制"
        FACT[事实检查]
        LOGIC[逻辑检查]
        CITE[引用生成]
    end
    
    QUERY --> CONTEXT
    DOCS --> CONTEXT
    CONTEXT --> TEMPLATE
    
    TEMPLATE --> PROMPT
    PROMPT --> OPTIMIZE
    OPTIMIZE --> GATEWAY
    
    GATEWAY --> BALANCE
    BALANCE --> GPT4
    BALANCE --> QWEN
    BALANCE --> LOCAL
    
    GPT4 --> PARSE
    QWEN --> PARSE
    LOCAL --> PARSE
    
    PARSE --> VALIDATE
    VALIDATE --> FORMAT
    FORMAT --> FACT
    
    FACT --> LOGIC
    LOGIC --> CITE
    
    FALLBACK --> LOCAL
```

## 6. 质量控制流程图

```mermaid
flowchart TD
    INPUT[生成结果] --> EXTRACT[信息提取]
    
    EXTRACT --> FACT[事实一致性检查]
    EXTRACT --> LOGIC[逻辑一致性检查]
    EXTRACT --> SOURCE[来源归属检查]
    EXTRACT --> CONF[置信度评估]
    
    FACT --> SCORE1[事实分数]
    LOGIC --> SCORE2[逻辑分数]
    SOURCE --> SCORE3[来源分数]
    CONF --> SCORE4[置信分数]
    
    SCORE1 --> COMBINE[综合评分]
    SCORE2 --> COMBINE
    SCORE3 --> COMBINE
    SCORE4 --> COMBINE
    
    COMBINE --> THRESHOLD{阈值检查}
    
    THRESHOLD -->|通过| CITATION[生成引用]
    THRESHOLD -->|失败| REJECT[拒绝结果]
    
    CITATION --> VALIDATE[引用验证]
    VALIDATE --> FINAL[最终结果]
    
    REJECT --> FEEDBACK[反馈信息]
    FEEDBACK --> RETRY[重新生成]
    
    RETRY --> INPUT
```

## 7. 缓存系统架构图

```mermaid
graph TB
    subgraph "应用层"
        APP[RAG应用]
    end
    
    subgraph "L1缓存 - 内存"
        L1[LRU内存缓存<br/>2GB容量<br/>1小时TTL]
    end
    
    subgraph "L2缓存 - Redis"
        L2[Redis集群<br/>20GB容量<br/>24小时TTL]
    end
    
    subgraph "L3缓存 - 磁盘"
        L3[磁盘缓存<br/>200GB容量<br/>7天TTL]
    end
    
    subgraph "数据源"
        DB[(数据库)]
        API[外部API]
        FILE[文件系统]
    end
    
    subgraph "缓存策略"
        QC[查询缓存]
        EC[嵌入缓存]
        RC[检索缓存]
        GC[生成缓存]
    end
    
    APP --> L1
    L1 -->|未命中| L2
    L2 -->|未命中| L3
    L3 -->|未命中| DB
    L3 -->|未命中| API
    L3 -->|未命中| FILE
    
    L1 -.->|回填| L2
    L2 -.->|回填| L3
    
    QC --> L1
    EC --> L2
    RC --> L2
    GC --> L3
```

## 8. 监控系统架构图

```mermaid
graph TB
    subgraph "数据收集"
        METRICS[指标收集器]
        LOGS[日志收集器]
        TRACES[链路追踪]
    end
    
    subgraph "RAG服务"
        RS1[RAG服务-1]
        RS2[RAG服务-2]
        RS3[RAG服务-3]
    end
    
    subgraph "存储层"
        PROM[(Prometheus)]
        ES[(Elasticsearch)]
        JAEGER[(Jaeger)]
    end
    
    subgraph "可视化"
        GRAFANA[Grafana仪表板]
        KIBANA[Kibana日志分析]
        JAEGER_UI[Jaeger追踪界面]
    end
    
    subgraph "告警"
        ALERT[告警管理器]
        WEBHOOK[Webhook通知]
        EMAIL[邮件通知]
        SMS[短信通知]
    end
    
    RS1 --> METRICS
    RS2 --> METRICS
    RS3 --> METRICS
    
    RS1 --> LOGS
    RS2 --> LOGS
    RS3 --> LOGS
    
    RS1 --> TRACES
    RS2 --> TRACES
    RS3 --> TRACES
    
    METRICS --> PROM
    LOGS --> ES
    TRACES --> JAEGER
    
    PROM --> GRAFANA
    ES --> KIBANA
    JAEGER --> JAEGER_UI
    
    PROM --> ALERT
    ALERT --> WEBHOOK
    ALERT --> EMAIL
    ALERT --> SMS
```
