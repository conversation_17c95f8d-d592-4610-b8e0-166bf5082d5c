# 图表可视化文档

本文件夹包含系统架构的各种可视化图表，使用Mermaid语法绘制。

## 📋 文档列表

| 文档名称 | 描述 | 状态 |
|---------|------|------|
| [架构图表集合.md](./架构图表集合.md) | 系统整体架构图表集合 | ✅ 完成 |
| [RAG架构图表集合.md](./RAG架构图表集合.md) | RAG系统专用架构图表 | ✅ 完成 |

## 📊 图表概述

可视化图表帮助理解系统架构、数据流向和组件关系，是技术文档的重要补充。

### 系统架构图表

#### 整体架构图
- 系统分层架构
- 组件关系图
- 数据流向图
- 部署架构图

#### 核心模块图
- 文档处理流程图
- AI分析流程图
- 用户交互流程图
- 数据存储架构图

### RAG系统图表

#### RAG架构图
- RAG系统整体架构
- RAG处理流程图
- 知识库管理架构
- 检索引擎架构

#### 技术实现图
- 生成引擎架构
- 质量控制流程
- 缓存系统架构
- 监控系统架构

### 图表类型

| 图表类型 | 用途 | 示例 |
|---------|------|------|
| 流程图 (flowchart) | 展示处理流程和决策逻辑 | RAG处理流程 |
| 架构图 (graph) | 展示系统组件和关系 | 系统整体架构 |
| 时序图 (sequenceDiagram) | 展示交互时序 | API调用流程 |
| 类图 (classDiagram) | 展示代码结构 | 核心类设计 |
| 状态图 (stateDiagram) | 展示状态变化 | 任务状态流转 |

### Mermaid语法示例

#### 基础流程图
```mermaid
flowchart TD
    A[开始] --> B{条件判断}
    B -->|是| C[处理A]
    B -->|否| D[处理B]
    C --> E[结束]
    D --> E
```

#### 系统架构图
```mermaid
graph TB
    subgraph "前端层"
        UI[用户界面]
    end
    
    subgraph "后端层"
        API[API服务]
        BIZ[业务逻辑]
    end
    
    subgraph "数据层"
        DB[(数据库)]
    end
    
    UI --> API
    API --> BIZ
    BIZ --> DB
```

### 图表使用指南

#### 查看图表
1. **在线查看**: 使用支持Mermaid的Markdown编辑器
2. **本地渲染**: 使用Mermaid CLI工具
3. **集成工具**: 在文档系统中集成Mermaid渲染

#### 编辑图表
1. **语法学习**: 参考Mermaid官方文档
2. **在线编辑**: 使用Mermaid Live Editor
3. **版本控制**: 图表代码纳入Git管理

#### 导出图表
```bash
# 安装Mermaid CLI
npm install -g @mermaid-js/mermaid-cli

# 导出为PNG
mmdc -i diagram.mmd -o diagram.png

# 导出为SVG
mmdc -i diagram.mmd -o diagram.svg
```

### 图表维护

#### 更新原则
- **及时更新**: 架构变更时同步更新图表
- **版本管理**: 重要变更保留历史版本
- **一致性**: 保持图表风格和命名一致

#### 质量标准
- **清晰易读**: 布局合理，标签清晰
- **准确完整**: 反映真实架构状态
- **简洁明了**: 突出重点，避免过度复杂

## 🎯 使用建议

1. **架构理解**: 先查看整体架构图，再深入具体模块
2. **流程梳理**: 通过流程图理解业务和技术流程
3. **问题定位**: 使用架构图快速定位问题范围
4. **方案设计**: 参考现有图表设计新的架构方案

## 🔗 相关工具

- [Mermaid官网](https://mermaid-js.github.io/mermaid/) - 官方文档和语法参考
- [Mermaid Live Editor](https://mermaid.live/) - 在线编辑器
- [VS Code插件](https://marketplace.visualstudio.com/items?itemName=bierner.markdown-mermaid) - Markdown中的Mermaid支持
