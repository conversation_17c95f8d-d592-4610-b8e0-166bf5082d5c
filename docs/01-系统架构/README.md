# 系统架构文档

本文件夹包含AI合同审查系统的整体架构设计文档。

## 📋 文档列表

| 文档名称 | 描述 | 状态 |
|---------|------|------|
| [技术架构设计.md](./技术架构设计.md) | 系统整体技术架构、分层设计、核心模块 | ✅ 完成 |
| [技术规格书.md](./技术规格书.md) | 详细技术规格、性能指标、接口定义 | ✅ 完成 |
| [技术选型与实施指南.md](./技术选型与实施指南.md) | 技术栈选择、开发环境、实施步骤 | ✅ 完成 |

## 🏗️ 架构概览

### 系统分层架构
```
┌─────────────────────────────────────────┐
│              用户界面层                  │
│        Vue3 + TypeScript + Element Plus │
├─────────────────────────────────────────┤
│              API网关层                   │
│           Nginx + 负载均衡               │
├─────────────────────────────────────────┤
│              业务服务层                  │
│    文档处理 | AI分析 | 风险评估 | 报告生成 │
├─────────────────────────────────────────┤
│              AI引擎层                    │
│         LangChain + RAG检索增强          │
├─────────────────────────────────────────┤
│              数据存储层                  │
│    PostgreSQL | Redis | ChromaDB | 文件  │
└─────────────────────────────────────────┘
```

### 核心技术栈
- **后端**: Python 3.11 + FastAPI + LangChain
- **前端**: Vue 3 + TypeScript + Element Plus  
- **数据库**: PostgreSQL + Redis + ChromaDB
- **AI模型**: OpenAI GPT-4 / 千问2.5 / Llama3
- **部署**: Docker + Docker Compose

## 🎯 阅读建议

1. **新手入门**: 先阅读 `技术架构设计.md` 了解整体架构
2. **技术选型**: 参考 `技术选型与实施指南.md` 了解技术决策
3. **详细规格**: 查看 `技术规格书.md` 了解具体技术要求
